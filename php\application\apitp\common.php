<?php
    // ------------------
// 脱敏函数（全局）
// ------------------

if (!function_exists('desensitize_phone')) {
    /**
     * 手机号脱敏（13812345678 => 138****5678）
     * @param string $phone
     * @return string
     */
    function desensitize_phone($phone)
    {
        if (!$phone || strlen($phone) < 7) return $phone;
        return substr($phone, 0, 3) . '****' . substr($phone, -4);
    }
}

if (!function_exists('desensitize_certificate')) {
    /**
     * 证件号脱敏（440982200001010012 => 440982********0012）
     * @param string $cert
     * @return string
     */
    function desensitize_certificate($cert)
    {
        if (!$cert || strlen($cert) < 10) return $cert;
        return substr($cert, 0, 6) . '********' . substr($cert, -4);
    }
}

if (!function_exists('desensitize_name')) {
    /**
     * 姓名脱敏（张三丰 => 张*丰，李小龙 => 李*龙）
     * @param string $name
     * @return string
     */
    function desensitize_name($name)
    {
        $len = mb_strlen($name, 'UTF-8');
        if ($len === 1) {
            return '*';
        } elseif ($len === 2) {
            return mb_substr($name, 0, 1, 'UTF-8') . '*';
        } elseif ($len === 3) {
            return mb_substr($name, 0, 1, 'UTF-8') . '*' . mb_substr($name, 2, 1, 'UTF-8');
        } elseif ($len >= 4) {
            return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($name, -1, 1, 'UTF-8');
        }
        return $name;
    }
}