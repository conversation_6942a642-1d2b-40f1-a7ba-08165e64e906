<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\Holidays;
use think\Cache;


/**
 * 示例接口
 */
class Venue extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
    }

    //todo,后台补充了才开发
    /**
     * 获取未来7天的场馆开放信息
     * @return Response
     */        
    public function getVenueInfo()
    {
         // 尝试从缓存读取
        // $cacheKey = 'holiday_state:holiday';   
        // $result = Cache::store('redis')->get($cacheKey);
        // if ($result) {
        //     return json(['code' => 1, 'msg' => 'success', 'data' => json_decode($result, true)]);
        // }

        // 当前日期
        $now = date('Y-m-d');
        // 第7天日期（含今天共7天）
        $end = date('Y-m-d', strtotime('+6 day'));

        // 查询 holidays 表
        $list = Holidays::name('holidays')
            ->field([
                'id',
                'holidays_date as day',
                'holidays_week as dayOfWeek',
                'is_close as isClose'
            ])
            ->where('holidays_date', '>=', $now)
            ->where('holidays_date', '<=', $end)
            ->order('holidays_date asc')
            ->select();
        // 写入 Redis 缓存，30 分钟
        // Cache::store('redis')->set($cacheKey, json_encode($list), 1800);

        return $this->success('获取成功', $list);
    }


    public function getVenueDetail()
    {
        // 获取入参
        $data = $this->request->getInput(); // 原始字符串
        $param = json_decode($data, true);
        // $param = $this->reques->post();
        if (empty($param['date'])) {
               // return $this->error('日期不能为空!');
        }

        $date = date('Y-m-d', strtotime($param['date']));
        $now = date('Y-m-d');
        $max = date('Y-m-d', strtotime('+6 days'));

        // 检查是否在合法范围内（今天~7天内）
        if ($date < $now || $date > $max) {
            return $this->error('场次过期或未开始!');
        }

        // $key = 'venue_state:' . $date;
        // $cached = Cache::store('redis')->get($key);
        // if ($cached) {
        //     return json(['code' => 1, 'msg' => 'success', 'data' => json_decode($cached, true)]);
        // }

        // 查询场次
        $list = \app\common\model\Venue::name('venue')
            ->alias('v')
            ->field([
                'v.id',
                'v.venue_poll'       => 'venuePoll',
                'v.venue_start_time' => 'venueStartTime',
                'v.venue_end_time'   => 'venueEndTime',
                'v.is_open'          => 'isOpen',
                'v.week',
                'v.inventory_votes'  => 'inventoryVotes',
            ])
            ->whereRaw("DATE_FORMAT(v.venue_start_time,'%y%m%d') = DATE_FORMAT(?, '%y%m%d')", [$date])
            ->where('v.del_flag', 0)
            ->where('v.is_open', 1)
            ->order('v.venue_start_time', 'asc')
            ->select();

        // // 写入缓存（3小时）
        // if ($list) {
        //     Cache::store('redis')->set($key, json_encode($list), 10800);
        // }

        return $this->success('获取成功', $list);
    }

    /**
     * 显示我的签到记录
     * @ApiTitle    (显示我的签到记录)
     * @ApiSummary  (获取用户的场馆预约和签到记录，支持分页)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/showMySign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="pageNum", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="pageSize", type="integer", required=false, description="每页数量，默认10")
     */
    public function showMySign()
    {
        
            // 获取用户ID - 对应Java中的wLoginTokenService.getUserId(request)
            $userId = $this->getUserId();
            // 获取分页参数 - 对应Java中的MyPageParam
            $pageNum = $this->request->param('pageNum', 1, 'intval');
            $pageSize = $this->request->param('pageSize', 10, 'intval');
            
            // 查询总数 - 对应Java中的venueService.selectCount(userId)
            $total = $this->getVenueSignCount($userId);
            $list = [];
            if ($total > 0) {
                // 计算分页参数 - 对应Java中的分页逻辑
                $start = ($pageNum - 1) * $pageSize;
                $end = $pageSize;
                
                // 查询分页数据 - 对应Java中的venueService.showMySign(userId, start, end)
                $list = $this->getVenueSignList($userId, $start, $end);
                // 处理过期状态 - 对应Java中VenueServiceImpl的状态更新逻辑
                $currentTime = time();
                foreach ($list as &$item) {
                    // 如果flag为"1"且当前时间已超过场馆结束时间，将flag更新为"2"
                    if ($item['flag'] == '1' && $currentTime > strtotime($item['venueEndTime'])) {
                        $item['flag'] = '2';
                    }
                }
            }
            // 返回结果 - 对应Java中的HashMap封装
            $result = [
                'total' => $total,
                'rows' => $list
            ];
            return $this->success('获取成功', $result);
            
        
    }

    /**
     * 获取用户场馆签到记录总数
     * @param int $userId 用户ID
     * @return int
     */
    private function getVenueSignCount($userId)
    {
        return \app\common\model\VenueSubscribe::alias('vs')
            ->join(['venue' => 'v'], 'vs.venue_id = v.id')
            ->where('vs.user_id', $userId)
            ->where('vs.del_flag', '0')
            ->where('v.del_flag', '0')
            ->count();
    }

    /**
     * 获取用户场馆签到记录列表
     * @param int $userId 用户ID
     * @param int $start 偏移量
     * @param int $end 每页数量
     * @return array
     */
    private function getVenueSignList($userId, $start, $end)
    {
        $result = \app\common\model\VenueSubscribe::alias('vs')
            ->join(['venue' => 'v'], 'vs.venue_id = v.id')
            ->join(['user_linkman' => 'ul'], 'vs.user_linkman_id = ul.id', 'LEFT')
            ->field([
                'v.id as venueId',
                'v.venue_start_time as venueStartTime',
                'v.venue_end_time as venueEndTime', 
                'v.week',
                'vs.number',
                'CASE WHEN vs.sign_state = "1" THEN "1" ELSE "0" END as flag',
                'vs.sign_state as signState',
                'vs.type as type',
                'vs.number'
            ])
            ->where('vs.user_id', $userId)
            ->where('vs.del_flag', '0')
            ->where('v.del_flag', '0')
            ->order('v.venue_start_time', 'desc')
            ->limit($start, $end)
            ->select()
            ; 
        $list = collection($result)->toArray();
        // 格式化数据 - 对应Java中WebUserCenterVo的字段结构
        foreach ($list as &$item) {
            // 时间格式化 - 对应Java中@JsonFormat(pattern="yyyy/MM/dd HH:mm:ss")
            $item['venueStartTime'] = date('Y/m/d H:i:s', strtotime($item['venueStartTime']));
            $item['venueEndTime'] = date('Y/m/d H:i:s', strtotime($item['venueEndTime']));
            // peopleCount字段 - 对应Java中WebUserCenterVo的peopleCount
            $item['peopleCount'] = (string)$item['number'];
        }

        return $list;
    }

    /**
     * 场馆预约
     * @ApiTitle    (场馆预约)
     * @ApiSummary  (用户预约场馆)
     * @ApiMethod   (POST)
     * @ApiRoute    (/apitp/venue/signVenue)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     * @ApiParams   (name="linkIds", type="array", required=true, description="联系人ID数组")
     * @ApiParams   (name="number", type="integer", required=true, description="预约人数")
     */
    public function signVenue()
    {
        $userId = $this->getUserId();
        $data = $this->request->getInput();
        $param = json_decode($data, true);
        
        if (empty($param['venueId']) || empty($param['linkIds']) || empty($param['number'])) {
            return $this->error('参数不完整');
        }

        $venueId = $param['venueId'];
        $linkIds = $param['linkIds'];
        $number = $param['number'];
        $param['userId'] = $userId;

        try {
            // 检查用户联系人是否已预约该场馆
            $this->selectUserLinkmanExistVenue($userId ,$linkIds);
            $this->checkUserLinkmanExistVenue($userId, $linkIds);
            
            // 执行预约
            $result = $this->doSignVenue($param);
            
            if ($result > 0) {
                return $this->success('预约成功');
            } else {
                return $this->error('预约失败');
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 团队场馆预约
     * @ApiTitle    (团队场馆预约)
     * @ApiSummary  (团队预约场馆)
     * @ApiMethod   (POST)
     * @ApiRoute    (/apitp/venue/teamSignVenue)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     * @ApiParams   (name="linkIds", type="array", required=true, description="联系人ID数组")
     * @ApiParams   (name="number", type="integer", required=true, description="预约人数")
     */
    public function teamSignVenue()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('用户未登录', null, 401);
        }

        $data = $this->request->getInput();
        $param = json_decode($data, true);
        
        if (empty($param['venueId']) || empty($param['linkIds']) || empty($param['number'])) {
            return $this->error('参数不完整');
        }

        $param['userId'] = $userId;

        try {
            $result = $this->doTeamSignVenue($param);
            
            if ($result > 0) {
                return $this->success('团队预约成功');
            } else {
                return $this->error('团队预约失败');
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 取消预约
     * @ApiTitle    (取消预约)
     * @ApiSummary  (取消场馆预约)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/cancelSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     * @ApiParams   (name="subscribeId", type="integer", required=true, description="预约ID")
     * @ApiParams   (name="type", type="integer", required=true, description="类型")
     * @ApiParams   (name="number", type="integer", required=true, description="人数")
     * @ApiParams   (name="peopleIds", type="array", required=true, description="人员ID数组")
     */
    public function cancelSign()
    {
        // 获取用户ID -
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('用户未登录', null, 401);
        }

        // 获取参数 
        $venueId = $this->request->param('venueId', 0, 'intval');
        $subscribeId = $this->request->param('subscribeId', 0, 'intval');
        $type = $this->request->param('type', 0, 'intval');
        $number = $this->request->param('number', 0, 'intval');
        $peopleIds = $this->request->param('peopleIds', []);
        
        // 参数验证
        if (!$venueId || !$subscribeId || !isset($type) || !$number) {
            return $this->error('参数不完整');
        }
        
        // 如果peopleIds是字符串，转换为数组
        if (is_string($peopleIds)) {
            $peopleIds = explode(',', $peopleIds);
        }
        $result = $this->doCancelSign($userId, $venueId, $subscribeId, $type, $number, $peopleIds);
        
        if ($result ) {
            return $this->success('取消成功');
        } else {
            return $this->error('取消失败');
        }
    }

    /**
     * 场馆签到
     * @ApiTitle    (场馆签到)
     * @ApiSummary  (用户场馆签到)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/centerSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     * @ApiParams   (name="linkId", type="integer", required=true, description="联系人ID")
     */
    public function centerSign()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('用户未登录', null, 401);
        }

        $venueId = $this->request->param('venueId', 0, 'intval');
        $linkId = $this->request->param('linkId', 0, 'intval');

        if (!$venueId || !$linkId) {
            return $this->error('参数不完整');
        }

        try {
            $result = $this->doCenterSign($venueId, $userId, $linkId);
            
            if ($result > 0) {
                // 处理相关电影预约状态
                $this->updateRelatedFilmSubscribe($userId, $linkId);
                return $this->success('签到成功');
            } else {
                return $this->error('签到失败');
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

   

    /**
     * 显示场馆凭证
     * @ApiTitle    (显示场馆凭证)
     * @ApiSummary  (显示用户在指定场馆的预约凭证信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/showVoucher)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     */
    public function showVoucher()
    {
        $userId = $this->getUserId();
        $venueId = $this->request->param('venueId', 0, 'intval');
        if (!$venueId) {
            return $this->error('场馆ID不能为空');
        }
        // 检查用户是否有该场馆的预约记录
        $subscribeList = \app\common\model\VenueSubscribe::where('venue_id', $venueId)
            ->where('user_id', $userId)
            ->where('del_flag', 0)
            ->select();
        if (!$subscribeList || count($subscribeList) == 0) {
            return $this->error('未查询到记录!');
        }
        // 查询详细的凭证信息
        $voucherData = $this->getVoucherData($venueId, $userId);
        
        return $this->success('获取成功', $voucherData);
      
    }

    /**
     * 获取凭证数据
     */
    private function getVoucherData($venueId, $userId)
    {
        // 执行联表查询获取凭证信息
        $sql = "SELECT v.week, v.venue_start_time as venueStartTime, v.venue_end_time as venueEndTime, 
                       s.sign_state as signState, s.type, s.number, s.id as subscribeId,
                       l.linkman_name as linkmanName, l.linkman_certificate as linkmanCertificate, 
                       l.linkman_age as linkmanAge, l.linkman_phone as linkmanPhone, l.id as linkId
                FROM venue v
                LEFT JOIN venue_subscribe s ON v.id = s.venue_id AND s.user_id = ? AND s.del_flag = 0
                JOIN user_linkman l ON l.id = s.user_linkman_id
                WHERE v.id = ?";
        
        $result = \think\Db::query($sql, [$userId, $venueId]);
        
        if (!$result || count($result) == 0) {
            throw new \Exception('未查询到凭证信息');
        }

        // 组装返回数据
        $firstRecord = $result[0];
        $centerVoucher = [
            'week' => (int)$firstRecord['week'],
            'venueStartTime' => date('Y/m/d H:i:s', strtotime($firstRecord['venueStartTime'])),
            'venueEndTime' => date('Y/m/d H:i:s', strtotime($firstRecord['venueEndTime'])),
            'signState' => $firstRecord['signState'],
            'subscribeId' => (int)$firstRecord['subscribeId'],
            'type' => (int)$firstRecord['type'],
            'number' => (int)$firstRecord['number'],
            'peoples' => []
        ];

        // 组装人员信息
        foreach ($result as $item) {
            $centerVoucher['peoples'][] = [
                'linkId' => (int)$item['linkId'],
                'linkmanName' => $item['linkmanName'],
                'linkmanCertificate' => $item['linkmanCertificate'],
                'linkmanPhone' => $item['linkmanPhone'],
                'linkmanAge' => (int)$item['linkmanAge']
            ];
        }

        return $centerVoucher;
    }

  
    /**
     * 获取场馆二维码
     * @ApiTitle    (获取场馆二维码)
     * @ApiSummary  (获取场馆二维码)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/getQrCode)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     * @ApiParams   (name="linkId", type="integer", required=true, description="联系人ID")
     */
    public function getQrCode()
    {
       
            // 获取用户ID 
            $userId = $this->getUserId();
           
            // 获取参数 
            $venueId = $this->request->param('venueId', 0, 'intval');
            $linkId = $this->request->param('linkId', 0, 'intval');
            
            // 参数验证
            if (!$venueId || !$linkId) {
                return $this->error('参数不完整');
            }
            
            // 调用业务逻辑 
            $qrCode = $this->getQrCodeBusiness($venueId, $userId, $linkId);
            
            // 返回结果 
            return $this->success('获取成功', $qrCode);
    }

    /**
     * 获取场馆二维码业务逻辑
     * @param int $venueId 场馆ID
     * @param int $userId 用户ID
     * @param int $linkId 联系人ID
     * @return string 二维码字符串
     * @throws \Exception
     */
    private function getQrCodeBusiness($venueId, $userId, $linkId)
    {
        // 构建Redis缓存键 
        $key = "venue_subscribe:{$venueId}:{$userId}:{$linkId}";
         
        // 尝试从Redis缓存获取 - 
        $qrCode = Cache::get($key);
        if ($qrCode) {
            return $qrCode;
        }
        
        // 查询预约记录 
        $venueSubscribeList = \app\common\model\VenueSubscribe::where('venue_id', $venueId)
            ->where('user_id', $userId)
            ->where('user_linkman_id', $linkId)
            ->where('del_flag', '0')
            ->select();
            
        if (!$venueSubscribeList || count($venueSubscribeList) == 0) {
            throw new \Exception('未查询到记录!');
        }
        
        $venueSubscribe = $venueSubscribeList[0];
        
        // 查询联系人信息 
        $userLinkman = \app\common\model\UserLinkman::where('id', $venueSubscribe['user_linkman_id'])
            ->where('del_flag', '0')
            ->find();
            
        if (!$userLinkman || $userLinkman['del_flag'] == '2') {
            throw new \Exception('联系人不存在!');
        }
        
        // 构建内容对象 
        $content = [
            'id' => $venueSubscribe['id'],
            'venueId' => $venueId,
            'userId' => $userId,
            'linkId' => $linkId,
            'subscribeState' => $venueSubscribe['subscribe_state'],
            'signState' => $venueSubscribe['sign_state'],
            'type' => $venueSubscribe['type'],
            'number' => $venueSubscribe['number'],
            'linkmanName' => $userLinkman['linkman_name'],
            'linkmanPhone' => $userLinkman['linkman_phone'],
            'linkmanAge' => $userLinkman['linkman_age'],
            'linkmanCertificate' => $userLinkman['linkman_certificate'],
            'linkmanCertificateType' => $userLinkman['linkman_certificate_type'],
            'showTime' => date('Y-m-d H:i:s') // 对应Java中的Coding.getCurrentTime()
        ];
        
        // 转换为JSON字符串 
        $text = json_encode($content, JSON_UNESCAPED_UNICODE);
        
        // 加密处理 
        $encryptedHex = $this->encryptDecryptHex($text);
        $qrCode = "venue:" . $encryptedHex;
        
        // 缓存2分钟 
        Cache::set($key, $qrCode, 120);
        
        return $qrCode;
    }
    
    /**
     * 加密并转换为16进制 
     * @param string $src 源字符串
     * @return string 加密后的16进制字符串
     */
    private function encryptDecryptHex($src)
    {
        try {
            // 转换为GBK编码的字节数组 
            $buf = iconv('UTF-8', 'GBK//IGNORE', $src);
            $bufBytes = [];
            for ($i = 0; $i < strlen($buf); $i++) {
                $bufBytes[] = ord($buf[$i]);
            }
            
            // 执行加密解密 
            $encryptedBytes = \app\common\library\DesEncryption::encryptDecryptBuf($bufBytes);
            
            // 转换为16进制字符串 
            return $this->byte2hex($encryptedBytes);
        } catch (\Exception $e) {
            return '';
        }
    }
    
    /**
     * 字节数组转16进制字符串 
     * @param array $bytes 字节数组
     * @return string 16进制字符串（大写）
     */
    private function byte2hex($bytes)
    {
        $hs = '';
        foreach ($bytes as $b) {
            $tmp = dechex($b & 0xFF);
            if (strlen($tmp) == 1) {
                $hs .= '0' . $tmp;
            } else {
                $hs .= $tmp;
            }
        }
        return strtoupper($hs);
    }

    /**
     * 获取管理员二维码
     * @ApiTitle    (获取管理员二维码)
     * @ApiSummary  (获取管理员二维码)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/getAdminCode)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function getAdminCode()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return $this->error('用户未登录', null, 401);
        }

        try {
            $qrCode = $this->generateAdminCode($userId);
            return $this->success('获取成功', $qrCode);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 身份证二维码签到
     * @ApiTitle    (身份证二维码签到)
     * @ApiSummary  (通过身份证二维码进行签到)
     * @ApiMethod   (POST)
     * @ApiRoute    (/apitp/venue/certificateSign)
     * @ApiParams   (name="data", type="string", required=true, description="加密的二维码数据")
     */
    public function certificateSign()
    {
        $data = $this->request->getInput();
        $param = json_decode($data, true);
        
        if (empty($param['data'])) {
            return $this->error('二维码数据不能为空');
        }

        try {
            // 使用与Java端一致的解密方式
            $qrData = $param['data'];
            $bytes = [];
            
            // 16进制字符串转字节数组
            for ($i = 0; $i < strlen($qrData); $i += 2) {
                $bytes[] = hexdec(substr($qrData, $i, 2));
            }
            
            // 使用与Java端相同的COMM_KEY进行异或解密
            $COMM_KEY = [105, 106, 109, 101, 100, 111, 107, 109, 102, 97, 111, 104, 102, 97, 111, 105];
            $j = 0;
            for ($i = 0; $i < count($bytes); $i++) {
                $bytes[$i] = ($bytes[$i] ^ $COMM_KEY[$j]) & 0xFF;
                if (++$j == count($COMM_KEY)) {
                    $j = 0;
                }
            }
            
            // 字节数组转字符串
            $decryptedText = '';
            foreach ($bytes as $byte) {
                $decryptedText .= chr($byte);
            }
            
            // 从GBK转换为UTF-8
            $strText = iconv('GBK', 'UTF-8//IGNORE', $decryptedText);
            $content = json_decode($strText, true);
            
            if (!$content) {
                return $this->error('二维码数据格式错误');
            }
            
            $sfid = $content['sfid'] ?? '';
            $name = $content['name'] ?? '';
            $showTime = $content['showTime'] ?? '';
            $codeType = $content['codeType'] ?? '0';
            
            // 验证二维码类型
            if (!empty($codeType) && $codeType !== '0') {
                return $this->error('非入场二维码');
            }
            
            // 验证必要信息
            if (empty($sfid) || empty($name) || empty($showTime)) {
                return $this->error('签到失败，信息不全');
            }
            
            // 验证时效性（10分钟内）
            $minutes = $this->getMinutesBetween($showTime, date('Y-m-d H:i:s'));
            if (abs($minutes) > 10) {
                return $this->error('二维码已过期');
            }
            
            // 处理签到逻辑
            $result = $this->processCertificateSign($content, $sfid, $name);
            
            return $this->success('签到成功', $result);
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    // 私有方法实现具体业务逻辑
    
    /**
     * 检查用户联系人是否已预约该场馆
     */
    private function checkUserLinkmanExistVenue($userId, $linkIds)
    {
        foreach ($linkIds as $linkId) {
            $count = \app\common\model\VenueSubscribe::where('user_id', $userId)
                ->where('user_linkman_id', $linkId)
                ->where('del_flag', '0')
                ->count();
            
            if ($count > 0) {
                throw new \Exception('您已报名/签到,如需更改预约人,请在未签到的情况下取消重报!');
            }
        }
    }

    private function selectUserLinkmanExistVenue($userId, $linkIds)
    {
        // 获取用户所有联系人ID
        $userLinkmanIds = \app\common\model\UserLinkman::where('user_id', $userId)
            ->where('del_flag', '0')
            ->column('id');
        
        // 验证请求的联系人是否都属于该用户
        foreach ($linkIds as $linkId) {
            if (!in_array($linkId, $userLinkmanIds)) {
                throw new \Exception('本次添加的联系人越权!');
            }
        }
    }
    
    /**
     * 执行场馆预约
     */
    private function doSignVenue($param)
    {
        $venueId = $param['venueId'];
        $userId = $param['userId'];
        $linkIds = $param['linkIds'];
        $number = $param['number'];
        
        // 获取场馆信息
        $venue = \app\common\model\Venue::where('id', $venueId)->find();
        if (!$venue) {
            throw new \Exception('该场次不存在,无法预约!');
        }
        
        if ($venue['del_flag'] == '2') {
            throw new \Exception('该场次不存在,无法预约!');
        }
        
        if ($venue['is_open'] == '0') {
            throw new \Exception('该场次暂时为不可预约状态,请联系管理员开启!');
        }
        
        $currentTime = time();
        if ($currentTime > strtotime($venue['venue_end_time'])) {
            throw new \Exception('时间已过,无法预约!');
        }
        
        // 检查余票
        if ($venue['inventory_votes'] < count($linkIds)) {
            throw new \Exception('场馆余票不足,请选择其他场次');
        }
        
        // 开始事务
        \think\Db::startTrans();
        try {
            // 更新余票
            \app\common\model\Venue::where('id', $venueId)
                ->setDec('inventory_votes', count($linkIds));
            
            // 保存预约记录
            $this->saveSubscribe($venueId, $userId, 0, count($linkIds), $linkIds);
            
            \think\Db::commit();
            return 1;
        } catch (\Exception $e) {
            \think\Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 执行团队场馆预约
     */
    private function doTeamSignVenue($param)
    {
        $venueId = $param['venueId'];
        $userId = $param['userId'];
        $number = $param['number'];
        
        if ($number < 1) {
            throw new \Exception('团队人数不能小于1!');
        }
        
        if (isset($param['minor']) && $param['minor'] > $number) {
            throw new \Exception('团队未成年人数不能大于团队人数!');
        }
        
        // 验证领队信息是否在联系人中
        $linkman = \app\common\model\UserLinkman::where('user_id', $userId)
            ->where('linkman_name', $param['leader'])
            ->where('linkman_phone', $param['mobile'])
            ->find();
            
        if (!$linkman) {
            throw new \Exception('请先在联系人管理中添加领队信息!');
        }
        
        // 检查是否已预约
        $userLinkmen = \app\common\model\UserLinkman::where('user_id', $userId)->select();
        foreach ($userLinkmen as $userLinkman) {
            $count = \app\common\model\VenueSubscribe::where('user_id', $userId)
                ->where('venue_id', $venueId)
                ->where('user_linkman_id', $userLinkman['id'])
                ->where('del_flag', '0')
                ->count();
            
            if ($count > 0) {
                throw new \Exception('您已报名/签到,如需更改预约人,请在未签到的情况下取消重报!');
            }
        }
        
        // 获取场馆信息
        $venue = \app\common\model\Venue::where('id', $venueId)->find();
        if (!$venue) {
            throw new \Exception('该场次不存在,无法预约!');
        }
        
        if ($venue['del_flag'] == '2') {
            throw new \Exception('该场次不存在,无法预约!');
        }
        
        if ($venue['is_open'] == '0') {
            throw new \Exception('该场次暂时为不可预约状态,请联系管理员开启!');
        }
        
        $currentTime = time();
        if ($currentTime > strtotime($venue['venue_end_time'])) {
            throw new \Exception('时间已过,无法预约!');
        }
        
        // 保存团队信息
        $teamData = [
            'venue_id' => $venueId,
            'user_id' => $userId,
            'link_id' => $linkman['id'],
            'status' => 1,
            'type' => $param['type'] ?? 1,
            'name' => $param['name'] ?? '',
            'number' => $number,
            'minor' => $param['minor'] ?? 0,
            'leader' => $param['leader'],
            'mobile' => $param['mobile'],
            'file_name' => $param['fileName'] ?? '',
            'file_url' => $param['fileUrl'] ?? '',
            'remark' => $param['remark'] ?? '',
            'del_flag' => '0',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        // 检查是否已有相同团队
        $existTeam = \app\common\model\Team::where('venue_id', $venueId)
            ->where('user_id', $userId)
            ->where('leader', $param['leader'])
            ->where('mobile', $param['mobile'])
            ->where('del_flag', '0')
            ->find();
            
        if ($existTeam) {
            // 更新团队信息
            $teamData['id'] = $existTeam['id'];
            $teamData['create_time'] = $existTeam['create_time'];
            return \app\common\model\Team::where('id', $existTeam['id'])->update($teamData);
        } else {
            // 新增团队
            return \app\common\model\Team::create($teamData) ? 1 : 0;
        }
    }
    

    
    /**
     * 执行场馆签到
     */
    private function doCenterSign($venueId, $userId, $linkId)
    {
        // 获取场馆信息
        $venue = \app\common\model\Venue::where('id', $venueId)->find();
        if (!$venue) {
            throw new \Exception('场馆不存在!');
        }
        
        // 查找预约记录
        $venueSubscribe = \app\common\model\VenueSubscribe::where('venue_id', $venueId)
            ->where('user_id', $userId)
            ->where('user_linkman_id', $linkId)
            ->where('del_flag', '0')
            ->find();
            
        if (!$venueSubscribe) {
            throw new \Exception('未获取到有效数据');
        }
        
        if ($venueSubscribe['sign_state'] == '1') {
            throw new \Exception('已签到,无需再次签到!');
        }
        
        $currentTime = time();
        $venueStartTime = strtotime($venue['venue_start_time']);
        $venueEndTime = strtotime($venue['venue_end_time']);
        
        // 获取当天日期
        $currentDate = date('Y-m-d');
        $venueDate = date('Y-m-d', $venueStartTime);
        
        // 检查是否为当天场次
        if ($currentDate != $venueDate) {
            throw new \Exception('非当天场次,无法签到!');
        }
        
        // 获取星期几
        $dayOfWeek = date('w', $venueStartTime);
        
        // 计算签到时间范围
        $beginOfDay = strtotime(date('Y-m-d', $venueStartTime));
        
        if ($dayOfWeek == 0 || $dayOfWeek == 6) { // 周末
            // 周末全天开放，签到时间为场次开始前30分钟到结束后30分钟
            $signStartTime = $venueStartTime - 30 * 60;
            $signEndTime = $venueEndTime + 30 * 60;
        } else { // 工作日
            // 工作日分上下午场
            $noon = $beginOfDay + 13 * 3600 + 31 * 60; // 13:31
            $afternoon = $beginOfDay + 17 * 3600 + 1 * 60; // 17:01
            
            if ($venueStartTime < $noon) {
                // 上午场：13:01-13:31可签到
                $signStartTime = $beginOfDay + 13 * 3600 + 1 * 60;
                $signEndTime = $noon;
            } else {
                // 下午场：17:01-17:31可签到
                $signStartTime = $afternoon;
                $signEndTime = $beginOfDay + 17 * 3600 + 31 * 60;
            }
        }
        
        // 检查签到时间
        if ($currentTime < $signStartTime || $currentTime > $signEndTime) {
            throw new \Exception('不在签到时间范围内!');
        }
        
        // 更新签到状态
        $result = \app\common\model\VenueSubscribe::where('id', $venueSubscribe['id'])
            ->update([
                'sign_state' => '1',
                'sign_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
        return $result ? 1 : 0;
    }
    
    /**
     * 更新相关电影预约状态（与Java端逻辑一致）
     */
    private function updateRelatedFilmSubscribe($userId, $linkId)
    {
        // 构建时间范围（与Java端一致：当前日期和前7天）
        $currentDate = new \DateTime();
        $last = $currentDate->format('Y-m-d');
        $currentDate->modify('-7 days');
        $first = $currentDate->format('Y-m-d');
        
        $recordStart = $first . ' 00:00:00';
        $recordEnd = $last . ' 23:59:59';
        
        // 查找符合条件的电影预约
        $filmSubscribes = \app\common\model\FilmSubscribe::where('user_id', $userId)
            ->where('user_linkman_id', $linkId)
            ->where('subscribe_state', '1')
            ->where('create_time', '>=', $recordStart)
            ->where('create_time', '<=', $recordEnd)
            ->select();
            
        $curTime = new \DateTime();
        $curDateStr = $curTime->format('Y-m-d');
        
        foreach ($filmSubscribes as $filmSubscribe) {
            // 获取电影场次信息
            $filmSession = \app\common\model\FilmSession::where('id', $filmSubscribe['film_seeion_id'])->find();
            
            if ($filmSession) {
                $filmStartTime = new \DateTime($filmSession['film_start_time']);
                $filmDateStr = $filmStartTime->format('Y-m-d');
                
                // 检查是否为当天的电影且预约状态为"1"
                if ($filmDateStr == $curDateStr && $filmSubscribe['subscribe_state'] == '1') {
                    // 更新预约状态为"4"（与Java端一致）
                    \app\common\model\FilmSubscribe::where('id', $filmSubscribe['id'])
                        ->update([
                            'subscribe_state' => '4',
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                }
            }
        }
    }
      
  
 
    
    /**
     * 生成管理员二维码
     */
    private function generateAdminCode($userId)
    {
        // 检查Redis缓存
        $cacheKey = 'venue_admin:' . $userId;
        $cachedQrCode = \think\Cache::store('redis')->get($cacheKey);
        if ($cachedQrCode) {
            return $cachedQrCode;
        }
        
        // 获取用户信息
        $user = \app\common\model\User::where('id', $userId)->find();
        if (!$user) {
            throw new \Exception('未找到管理员信息!');
        }
        
        // 验证用户状态
        if ($user['del_flag'] != '0') {
            throw new \Exception('管理员已删除!');
        }
        
        if ($user['status'] != '0') {
            throw new \Exception('管理员已停用!');
        }
        
        // 验证管理员权限
        $hasAdminRole = $this->checkAdminRole($userId);
        if (!$hasAdminRole) {
            throw new \Exception('非管理员无此功能!');
        }
        
        // 构造管理员二维码数据（与Java端保持一致）
        $qrData = [
            'userId' => $userId,
            'nickName' => $user['nickname'] ?? '',
            'userName' => $user['username'] ?? '',
            'phonenumber' => $user['mobile'] ?? '',
            'showTime' => date('Y-m-d H:i:s')
        ];
        
        // 转换为JSON字符串
        $jsonText = json_encode($qrData, JSON_UNESCAPED_UNICODE);
        
        // 使用与Java端Coding.encryptDecryptHex一致的加密方式
        $dataWithPrefix = 'admin:' . $jsonText;
        $qrCode = $this->encryptDecryptHex($dataWithPrefix);
        
        // 缓存2分钟（与Java端一致）
        \think\Cache::store('redis')->set($cacheKey, $qrCode, 120);
        
        return $qrCode;
    }
    
    /**
     * 检查用户是否具有管理员权限
     */
    private function checkAdminRole($userId)
    {
        // 查询用户角色关联
        $userRoles = \app\common\model\UserRole::where('user_id', $userId)
            ->column('role_id');
            
        if (empty($userRoles)) {
            return false;
        }
        
        // 查询角色信息，检查是否有admin角色
        $adminRole = \app\common\model\Role::whereIn('id', $userRoles)
            ->where('role_key', 'admin')
            ->where('status', '0')
            ->where('del_flag', '0')
            ->find();
            
        return !empty($adminRole);
    }
    
  
    
    /**
     * 计算两个时间之间的分钟差
     */
    private function getMinutesBetween($time1, $time2)
    {
        $timestamp1 = strtotime($time1);
        $timestamp2 = strtotime($time2);
        return ($timestamp2 - $timestamp1) / 60;
    }
    
    /**
     * 处理身份证签到
     */
    private function processCertificateSign($content, $sfid, $name)
    {
        $id = $content['id'] ?? null;
        $venueId = $content['venueId'] ?? null;
        $userId = $content['userId'] ?? null;
        
        $curVenueSubscribe = null;
        $curVenue = null;
        
        if ($id && $venueId && $userId) {
            // 直接查询预约和场馆信息
            $curVenueSubscribe = \app\common\model\VenueSubscribe::where('id', $id)->find();
            $curVenue = \app\common\model\Venue::where('id', $venueId)->find();
        } else {
            // 根据姓名和身份证查找联系人
            $linkmen = \app\common\model\UserLinkman::where('linkman_name', $name)
                ->where('linkman_certificate', $sfid)
                ->select();
                
            if (!$linkmen || count($linkmen) == 0) {
                throw new \Exception('未找到联系人信息');
            }
            
            // 构建联系人ID字符串（与Java端格式一致）
            $linkIds = array_column($linkmen->toArray(), 'id');
            $linkIdsStr = '(' . implode(',', $linkIds) . ')';
            
            // 查找7天内的预约信息
            $currentDate = date('Y-m-d');
            $startTime = date('Y-m-d', strtotime('-7 days')) . ' 00:00:00';
            $endTime = $currentDate . ' 23:59:59';
            
            $venueSubscribes = \app\common\model\VenueSubscribe::whereIn('user_linkman_id', $linkIds)
                ->where('create_time', '>=', $startTime)
                ->where('create_time', '<=', $endTime)
                ->select();
                
            if (!$venueSubscribes || count($venueSubscribes) == 0) {
                throw new \Exception('未找到预约信息');
            }
            
            // 找到当天最早开始的有效场馆（与Java端逻辑一致）
            $currentTime = new \DateTime();
            $currentDateStr = $currentTime->format('Y-m-d');
            
            foreach ($venueSubscribes as $venueSubscribe) {
                $venue = \app\common\model\Venue::where('id', $venueSubscribe['venue_id'])->find();
                if (!$venue) continue;
                
                $venueStartTime = new \DateTime($venue['venue_start_time']);
                $venueEndTime = new \DateTime($venue['venue_end_time']);
                
                // 检查是否为当天场馆且时间有效
                if ($venueStartTime->format('Y-m-d') == $currentDateStr &&
                    $venueStartTime > $currentTime &&
                    $venueEndTime > $currentTime) {
                    
                    // 选择开始时间最早的场馆
                    if (!$curVenue || $curVenue['venue_start_time'] > $venue['venue_start_time']) {
                        $curVenue = $venue;
                        $curVenueSubscribe = $venueSubscribe;
                    }
                }
            }
        }
        
        if (!$curVenueSubscribe) {
            throw new \Exception('未找到预约信息');
        }
        
        // 执行签到（添加对"已签到"异常的特殊处理）
        try {
            $signResult = $this->doCenterSign($curVenue['id'], $curVenueSubscribe['user_id'], $curVenueSubscribe['user_linkman_id']);
            if ($signResult <= 0) {
                throw new \Exception('签到失败');
            }
        } catch (\Exception $e) {
            // 如果包含"已签到"，不抛出错误（与Java端逻辑一致）
            if (strpos($e->getMessage(), '已签到') === false) {
                throw $e;
            }
        }
        
        // 更新相关电影预约状态
        $this->updateRelatedFilmSubscribe($curVenueSubscribe['user_id'], $curVenueSubscribe['user_linkman_id']);
        
        // 返回签到信息（与Java端格式一致）
        return [
            'name' => $name,
            'sfid' => $sfid,
            'startTime' => $curVenue['venue_start_time'],
            'endTime' => $curVenue['venue_end_time'],
            'venueId' => $curVenue['id'],
            'subscribeId' => $curVenueSubscribe['id'],
            'linkId' => $curVenueSubscribe['user_linkman_id'],
            'totalPoll' => $curVenue['venue_poll'],
            'inventoryVotes' => $curVenue['inventory_votes']
        ];
    }

    /**
     * 保存预约记录
     */
    private function saveSubscribe($venueId, $userId, $type, $number, $linkIds)
    {
        foreach ($linkIds as $linkId) {
            $subscribeData = [
                'venue_id' => $venueId,
                'user_id' => $userId,
                'user_linkman_id' => $linkId,
                'subscribe_type' => $type,
                'number' => $number,
                'sign_state' => '0',
                'subscribe_state' => '1',
                'del_flag' => '0',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            \app\common\model\VenueSubscribe::create($subscribeData);
        }
    }
    
    /**
     * 删除预约记录
     */
    private function deleteRecord($userId, $venueId, $peopleIds)
    {
        if ($peopleIds && count($peopleIds) > 0) {
            \app\common\model\VenueSubscribe::where('user_id', $userId)
                ->where('venue_id', $venueId)
                ->whereIn('user_linkman_id', $peopleIds)
                ->update(['del_flag' => '2', 'update_time' => date('Y-m-d H:i:s')]);
        }
    }

    /**
     * 执行取消预约 - 对应Java中VenueServiceImpl.cancelSign方法
     * @param int $userId 用户ID
     * @param int $venueId 场馆ID
     * @param int $subscribeId 预约ID
     * @param int $type 类型（1表示全部取消，其他值表示部分取消）
     * @param int $number 取消数量
     * @param array $peopleIds 人员ID数组
     * @return int
     */
    private function doCancelSign($userId, $venueId, $subscribeId, $type, $number, $peopleIds)
    {
        // 验证用户是否有该场馆的预约记录且未完成签到 - 对应Java中veryIfHasSign
        $stoke = \app\common\model\VenueSubscribe::where('user_id', $userId)
            ->where('venue_id', $venueId)
            ->where('del_flag', '0')
            ->where('sign_state', '0') // 未签到
            ->count();
            
        if ($stoke <= 0) {
            throw new \Exception('取消失败,未预约场馆或已完成签到!');
        }
        
        // 根据type参数确定要释放的库存数量 
        if ($type == 1) {
            $stoke = $number;
        } else if ($peopleIds && count($peopleIds) > 0 && $stoke > count($peopleIds)) {
            $stoke = count($peopleIds);
        }
        
        // 开始事务 
        \think\Db::startTrans();
    
        // 获取场馆信息 
        $venue = \app\common\model\Venue::where('id', $venueId)->find();
        if (!$venue) {
            throw new \Exception('场馆不存在!');
        }
        
        // 时间校验 
        $currentTime = time();
        if ($currentTime > strtotime($venue['venue_end_time'])) {
            throw new \Exception('上/下午场时间已过,无法取消!');
        }
        
        // 更新场馆库存 -
        \app\common\model\Venue::where('id', $venueId)
            ->setInc('inventory_votes', $stoke);
        
        // 删除预约记录 
        if ($type == 1) {
            // 删除整个预约记录
            \app\common\model\VenueSubscribe::where('id', $subscribeId)
                ->update(['del_flag' => '2', 'update_time' => date('Y-m-d H:i:s')]);
        } else {
            // 删除指定人员的预约记录
            $this->deleteRecord($userId, $venueId, $peopleIds);
        }
        
        // 异步更新缓存 - 对应Java中的CompletableFuture.runAsync
        $this->updateVenueCacheAsync($venue, $stoke);
        
        \think\Db::commit();
        return 1;
    
    }
    
    /**
     * 异步更新场馆缓存 - 对应Java中的Redis缓存更新逻辑
     * @param array $venue 场馆信息
     * @param int $stoke 释放的库存数量
     */
    private function updateVenueCacheAsync($venue, $stoke)
    {
    
        // 对应Java中的Redis缓存key格式
        $key = 'venue_state:' . date('Y-m-d', strtotime($venue['venue_start_time']));
        
        // 尝试从缓存获取数据
        $cacheData = \think\Cache::store('redis')->get($key);
        
        if ($cacheData) {
            $webVenueResultVos = json_decode($cacheData, true);
            
            if ($webVenueResultVos && is_array($webVenueResultVos)) {
                // 更新对应场馆的库存信息
                foreach ($webVenueResultVos as &$webVenueResultVo) {
                    if ($webVenueResultVo['id'] == $venue['id']) {
                        $webVenueResultVo['inventoryVotes'] = $webVenueResultVo['inventoryVotes'] + $stoke;
                        break;
                    }
                }
                
                // 重新写入缓存，3小时过期 - 对应Java中的3l, TimeUnit.HOURS
                \think\Cache::store('redis')->set($key, json_encode($webVenueResultVos), 10800);
            }
        }
        
    }

     private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }

}