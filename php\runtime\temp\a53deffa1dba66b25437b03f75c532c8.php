<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:73:"/mnt/f/kejiguan/php/public/../application/admin/view/dashboard/index.html";i:1753846497;s:62:"/mnt/f/kejiguan/php/application/admin/view/layout/default.html";i:1753348167;s:59:"/mnt/f/kejiguan/php/application/admin/view/common/meta.html";i:1753348167;s:61:"/mnt/f/kejiguan/php/application/admin/view/common/script.html";i:1753348167;}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
<title><?php echo (isset($title) && ($title !== '')?$title:''); ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="referrer" content="never">
<meta name="robots" content="noindex, nofollow">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<!-- Loading Bootstrap -->
<link href="/assets/css/backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">

<?php if(\think\Config::get('fastadmin.adminskin')): ?>
<link href="/assets/css/skins/<?php echo htmlentities(\think\Config::get('fastadmin.adminskin') ?? ''); ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">
<?php endif; ?>

<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config:  <?php echo json_encode($config ?? ''); ?>
    };
</script>

    </head>

    <body class="inside-header inside-aside <?php echo defined('IS_DIALOG') && IS_DIALOG ? 'is-dialog' : ''; ?>">
        <div id="main" role="main">
            <div class="tab-content tab-addtabs">
                <div id="content">
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            <section class="content-header hide">
                                <h1>
                                    <?php echo __('Dashboard'); ?>
                                    <small><?php echo __('Control panel'); ?></small>
                                </h1>
                            </section>
                            <?php if(!IS_DIALOG && !\think\Config::get('fastadmin.multiplenav') && \think\Config::get('fastadmin.breadcrumb')): ?>
                            <!-- RIBBON -->
                            <div id="ribbon">
                                <ol class="breadcrumb pull-left">
                                    <?php if($auth->check('dashboard')): ?>
                                    <li><a href="dashboard" class="addtabsit"><i class="fa fa-dashboard"></i> <?php echo __('Dashboard'); ?></a></li>
                                    <?php endif; ?>
                                </ol>
                                <ol class="breadcrumb pull-right">
                                    <?php foreach($breadcrumb as $vo): ?>
                                    <li><a href="javascript:;" data-url="<?php echo htmlentities($vo['url'] ?? ''); ?>"><?php echo htmlentities($vo['title'] ?? ''); ?></a></li>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                            <!-- END RIBBON -->
                            <?php endif; ?>
                            <div class="content">
                                
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <?php echo build_heading(null, false); ?>
    </div>
    <div class="panel-body">
        <div class="row">
            <!-- 入馆预约 -->
            <div class="col-md-4">
                <div class="box box-solid">

                    <div class="panel panel-default panel-intro">
                        <div class="form-inline"
                            style="display: flex; justify-content: space-between; align-items: center;">
                            <strong style="font-size: 18px;">入馆预约</strong>
                            <input type="text" id="venue-date" class="form-control datetimepicker" style="width: 200px;">
                        </div>
                        <div class="panel-body">


                            <div id="venue-chart1" style="width: 100%; height: 400px;"></div>
                            <div style="display: flex; justify-content: space-between; margin-top: 30px;">
                                <div style="width: 48%; height: 300px; position: relative;">
                                    <div id="venue-chart2" style="width: 100%; height: 100%;"></div>
                                    <div
                                        style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); font-size: 20px;">
                                        上午场</div>
                                </div>
                                <div style="width: 48%; height: 300px; position: relative;">
                                    <div id="venue-chart3" style="width: 100%; height: 100%;"></div>
                                    <div
                                        style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); font-size: 20px;">
                                        下午场</div>
                                </div>
                            </div>

                            <div style="text-align:center; font-size: 16px; margin-top: 20px;">
                                已预约人数：<span id="totalRegister" style="color:#ffba38;">0人</span>　
                                已签到人数：<span id="totalSign" style="color:#ffba38;">0人</span>
                            </div>
                        </div>
                    </div>


                </div>
            </div>

            <!-- 观影预约 -->
            <div class="col-md-4">
                <div class="box box-solid">


                    <div class="panel panel-default panel-intro">

                        <div class="form-inline"
                            style="display: flex; justify-content: space-between; align-items: center;">
                            <strong style="font-size: 18px;">观影预约</strong>
                            <input type="text" id="film-date" class="form-control datetimepicker" style="width: 200px;">
                        </div>

                        <div class="panel-body">
                            <div id="film-charts" style="width: 100%; height: 400px;"></div>

                            <div id="film-list" style="margin-top: 20px;"></div>

                            <div style="margin-top: 20px; font-weight: bold; font-size: 15px;">
                                <div> 4D影院共放映：
                                    <span id="filmType1Count" style="color: #ffba38;">0场</span>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                    核销人数：
                                    <span id="filmType1HadTicket" style="color: #ffba38;">0人</span>
                                </div>
                                <div style="margin-top: 8px;">

                                    球幕影院共放映：
                                    <span id="filmType0Count" style="color: #ffba38;">0场</span>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                    核销人数：
                                    <span id="filmType0HadTicket" style="color: #ffba38;">0人</span>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>

            <!-- 课程预约 -->
            <div class="col-md-4">
                <div class="box box-solid">


                    <div class="panel panel-default panel-intro">
                       
                          <div class="form-inline" style="display: flex; justify-content: space-between; align-items: center;">
                            <strong style="font-size: 18px;">课程预约</strong>
                            <input type="text" id="course-date" class="form-control datetimepicker" style="width: 200px;">
                          </div>
                      
                        <div class="panel-body">
                          <div id="course-charts" style="width: 100%; height: 400px;"></div>
                      
                          <div id="course-list" style="margin-top: 20px;"></div>
                      
                          <div style="margin-top: 20px; font-weight: bold; font-size: 15px;">
                            课程共发布：
                            <span id="courseCount" style="color: #ffba38;">0场</span>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            共签到人数：
                            <span id="hadSignCount" style="color: #ffba38;">0人</span>
                          </div>
                        </div>
                      </div>
                      



                </div>
            </div>
        </div>

    </div>
</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="/assets/js/require.min.js" data-main="/assets/js/require-backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.js?v=<?php echo htmlentities($site['version'] ?? ''); ?>"></script>

    </body>
</html>
