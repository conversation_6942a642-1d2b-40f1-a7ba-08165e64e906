import Vue from 'vue'
import { signatureGenerate } from "../utils/signatureUtil"
// const BASE_URL = 'https://api-hmugo-web.itheima.net' //域名或选取所有接口不变的那一部分
// const BASE_URL = 'http://10.10.50.11:8201'    // 域名或选取所有接口不变的那一部分
// const BASE_URL = 'http://10.10.30.10:8825'    // 域名或选取所有接口不变的那一部分
// const BASE_URL = 'https://baoankejiguan-api.iwesalt.com' //域名或选取所有接口不变的那一部分
// const BASE_URL = 'https://bakjgyyxt.baoan.gov.cn'
// const BASE_URL = 'http://baoankejiguan-admin.iwesalt.com'
const BASE_URL = 'http://192.168.48.193:8081'

export const myRequest = (options) => {
	let token = uni.getStorageSync('token');
	if(!options.noLoadingFlag){
		uni.showLoading({
			title:'正在加载中...'
		})		
	}
	return new Promise((resolve, reject) => {

		let config = {
			url: BASE_URL + options.url,
			method: options.method || 'GET',
			data: options.data || {},
			success: (res) => {
				// console.log(res)
				// console.log(token);
				// if (res.data.code !== 200) { 
				// 	return uni.showToast({
				// 		title: "数据获取失败！"
				// 	})
				// }
				// console.log(res)
				switch (res.data.code) {
					case 401:
						uni.removeStorageSync('token');
						uni.showModal({
							title: '提示',
							content: '登录状态已过期，您可以继续留在该页面，或者重新登录',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: `/pages/index/index`
									});
								} else if (res.cancel) {
									console.log('用户点击取消');
								}
							}
						})
						break;
					case 500:
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							showCancel: false
						})
						break;
					default:
						resolve(res);
						break;
				}
			},
			fail: (err) => {
				uni.showToast({
					title: "请求接口失败！",
					icon:'error'
				})
				reject(err);
			},
			complete: () => {
				uni.hideLoading()
			}
		};

		if (options.url.includes(`/wx/user/${Vue.prototype.appID}/login`)) {
			delete config['header'];
		} else {
			// 获取请求头参数
			const { signature, timestamp } = signatureGenerate(config);
			config['header'] = {
				Authorization: 'wx ' + token,
				sign: signature,
				timestamp: timestamp 
			};
		};

		uni.request(config);
	})
}
