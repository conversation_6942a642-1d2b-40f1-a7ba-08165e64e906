# nginx
## 测试服
### 访问后台的
1.1（模拟正式服， 只能在内网使用）
1.2 里面还有java的服务，一堆东西

https://www.j8j0.com/

```在service加入这几段
# PHP 项目处理：只拦截 /admin 开头的请求
location ~ ^/admin(/.*)?$ {
    root /www/wwwroot/kejiguan.17816888.xyz/kejiguan/php/public;# 修改为你 PHP 项目的 public 目录
    index index.php;
    # 将 /admin、/admin/xxx 都 rewrite 到 index.php?s=admin/xxx
    rewrite ^/admin(.*)$ /index.php?s=admin$1 last;
}

# PHP 文件处理（用于上面 rewrite 后进入的 index.php）
location ~ \.php($|/) {
    root /www/wwwroot/kejiguan.17816888.xyz/kejiguan/php/public; # 同样修改为你的 PHP 项目路径
    fastcgi_pass 127.0.0.1:9000; #  用宿主机 IP 访问容器
    fastcgi_index index.php;
    # include        fastcgi.conf;

    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    fastcgi_param QUERY_STRING $query_string;
    fastcgi_param REQUEST_METHOD $request_method;
    fastcgi_param CONTENT_TYPE $content_type;
    fastcgi_param CONTENT_LENGTH $content_length;

    fastcgi_param SCRIPT_NAME $fastcgi_script_name;
    fastcgi_param REQUEST_URI $request_uri;
    fastcgi_param DOCUMENT_URI $document_uri;
    fastcgi_param DOCUMENT_ROOT $document_root;
    fastcgi_param SERVER_PROTOCOL $server_protocol;
    fastcgi_param REQUEST_SCHEME $scheme;
    fastcgi_param HTTPS $https if_not_empty;

    fastcgi_param GATEWAY_INTERFACE CGI/1.1;
    fastcgi_param SERVER_SOFTWARE nginx/$nginx_version;

    fastcgi_param REMOTE_ADDR $remote_addr;
    fastcgi_param REMOTE_PORT $remote_port;
    fastcgi_param SERVER_ADDR $server_addr;
    fastcgi_param SERVER_PORT $server_port;
    fastcgi_param SERVER_NAME $server_name;

    # PHP only, required if PHP was built with --enable-force-cgi-redirect
    fastcgi_param REDIRECT_STATUS 200;
    fastcgi_param HTTP_HOST $host;

    # include        pathinfo.conf;
    set $real_script_name $fastcgi_script_name;
    if ($fastcgi_script_name ~ "^(.+?\.php)(/.+)$") {
        set $real_script_name $1;
        set $path_info $2;
    }
    fastcgi_param SCRIPT_FILENAME $document_root$real_script_name;
    fastcgi_param SCRIPT_NAME $real_script_name;
    fastcgi_param PATH_INFO $path_info;
}
# ⑤ 静态资源访问：assets、template、uploads
location /assets/ {
    root /www/wwwroot/kejiguan.17816888.xyz/kejiguan/php/public; # 同样修改为你的 PHP 项目路径
}

location /template/ {
    root /www/wwwroot/kejiguan.17816888.xyz/kejiguan/php/public; # 同样修改为你的 PHP 项目路径
}

location /uploads/ {
    root /www/wwwroot/kejiguan.17816888.xyz/kejiguan/php/public; # 同样修改为你的 PHP 项目路径
}
```



2. 给小程序的
2.1 （模拟正式服， 暴露在外网，但是只能访问小程序，不能访问admin）
2.2 里面还有java的服务，一堆东西

http://java.j8j0.com/




```在service加入这几段
# PHP 项目处理：只拦截 /apitp 开头的请求
location ~ ^/apitp(/.*)?$ {
    root /www/wwwroot/kejiguan.17816888.xyz/kejiguan/php/public;# 修改为你 PHP 项目的 public 目录
    index index.php;
    # 将 /apitp、/apitp/xxx 都 rewrite 到 index.php?s=apitp/xxx
    rewrite ^/apitp(.*)$ /index.php?s=apitp$1 last;
}

# PHP 文件处理（用于上面 rewrite 后进入的 index.php）
location ~ \.php($|/) {
    root /www/wwwroot/kejiguan.17816888.xyz/kejiguan/php/public; # 同样修改为你的 PHP 项目路径
    fastcgi_pass 127.0.0.1:9000; #  用宿主机 IP 访问容器
    fastcgi_index index.php;
    # include        fastcgi.conf;

    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    fastcgi_param QUERY_STRING $query_string;
    fastcgi_param REQUEST_METHOD $request_method;
    fastcgi_param CONTENT_TYPE $content_type;
    fastcgi_param CONTENT_LENGTH $content_length;

    fastcgi_param SCRIPT_NAME $fastcgi_script_name;
    fastcgi_param REQUEST_URI $request_uri;
    fastcgi_param DOCUMENT_URI $document_uri;
    fastcgi_param DOCUMENT_ROOT $document_root;
    fastcgi_param SERVER_PROTOCOL $server_protocol;
    fastcgi_param REQUEST_SCHEME $scheme;
    fastcgi_param HTTPS $https if_not_empty;

    fastcgi_param GATEWAY_INTERFACE CGI/1.1;
    fastcgi_param SERVER_SOFTWARE nginx/$nginx_version;

    fastcgi_param REMOTE_ADDR $remote_addr;
    fastcgi_param REMOTE_PORT $remote_port;
    fastcgi_param SERVER_ADDR $server_addr;
    fastcgi_param SERVER_PORT $server_port;
    fastcgi_param SERVER_NAME $server_name;

    # PHP only, required if PHP was built with --enable-force-cgi-redirect
    fastcgi_param REDIRECT_STATUS 200;
    fastcgi_param HTTP_HOST $host;

    # include        pathinfo.conf;
    set $real_script_name $fastcgi_script_name;
    if ($fastcgi_script_name ~ "^(.+?\.php)(/.+)$") {
        set $real_script_name $1;
        set $path_info $2;
    }
    fastcgi_param SCRIPT_FILENAME $document_root$real_script_name;
    fastcgi_param SCRIPT_NAME $real_script_name;
    fastcgi_param PATH_INFO $path_info;
}
```






## 正式服













## 发布 uni【未整理】

```bash
# 克隆项目


# 进入项目目录

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com/

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

# 发布 后端 【已整理】
**这里是怎么制作一个php容器，然后可以跟nginx联动**
确定路径：假设本次nginx的root是：/var/www/baoanphp
那么git pull到 /var/www/baoanphp
根目录.env文件的CODE_PATH、CONTAINER_PATH、：/var/www/baoanphp/php

```bash
git clone https://github.com/xxx/your-project.git /var/www/baoanphp
# 确保 PHP 项目目录可访问、可写
sudo chown -R www-data:www-data /var/www/baoanphp/php
sudo chmod -R 777 /var/www/baoanphp/php/runtime

软链接 nginx 配置到Nginx 宿主机（如果是nginx-docker 看映射目录）
sudo ln -s /var/www/baoanphp/docker/nginx/baoanphp.conf /etc/nginx/conf.d/baoanphp.conf
测试并重载 Nginx：
nginx -t
systemctl reload nginx

启动 PHP 容器
修改根目录.env文件： /var/www【根据nginx能访问到的路径确定】  /baoanphp/php【固定的，php放在这里】
cd /var/www/baoanphp
docker-compose up -d --build


```


# 本地开发

## PHP-lung
redis-server --daemonize yes
sudo service php7.4-fpm start
sudo service nginx start
http://172.26.224.80:8081/
http://192.168.193.17:8081/admin

define('THINK_VERSION', '5.0.28'); https://doc.thinkphp.cn/v5_0/default.html

//一键压缩打包后台的JS和CSS
php think min -m backend -r all

## php 斌

sudo systemctl restart redis




## uni-app
1. 下载hbuilder 3.4.7  https://pan.baidu.com/s/1kvigifhSaEImD-gCx_DQ8Q#list/path=%2Fsharelink291282757-664376222114347%2FHBuilderX%2F3.4.7&parentPath=%2Fsharelink291282757-664376222114347 提取码: bmnh
2. 小程序开发者工具
3. 安装node 版本【12.13.0】
4. 安装依赖 npm install --registry=https://registry.npmmirror.com/
5. 【非必须】 \uni\api\api.js， 修改里面的`BASE_URL`
5. hubilder : 运行->运行到小程序模拟器
6. 微信开发者工具：选择：  \uni\unpackage\dist\dev\mp-weixin\  目录
