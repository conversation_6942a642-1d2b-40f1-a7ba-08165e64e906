<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\FilmSession as FilmSessionModel;
use app\common\model\FilmSubscribe;
use app\common\model\FilmBlackList;
use app\common\model\UserLinkman;
use app\common\model\WebUser;
use think\Request;
use think\Cache;
use think\Validate;
use think\exception\ValidateException;
use think\Db;
use fast\Random;

/**
 * 影片场次控制器
 * @ApiWeigh (98)
 */
class FilmSession extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 获取影片场次详情
     * 
     * @ApiTitle    (获取影片场次详情)
     * @ApiSummary  (根据ID获取影片场次的详细信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/{id})
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="id", type="integer", required=true, description="影片场次ID")
     */
    public function getInfo()
    {
        $id = $this->request->param('id');
        
        if (!$id) {
            $this->error('参数错误');
        }
        
        $filmSession = FilmSessionModel::where('id', $id)->find();
        
        if (!$filmSession) {
            $this->error('影片场次不存在');
        }
        
        $this->success('获取成功', $filmSession);
    }

    /**
     * 根据场次ID查询排期信息
     * 
     * @ApiTitle    (查询排期信息)
     * @ApiSummary  (根据场次ID查询排期详情)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/selectByFileSessionId)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="fileSessionId", type="integer", required=true, description="影片场次ID")
     */
    public function selectByFileSessionId()
    {
        $fileSessionId = $this->request->param('fileSessionId');
        
        if (!$fileSessionId) {
            $this->error('参数错误');
        }
        
        // 查询排期信息，需要关联影片信息
        $schedule = FilmSessionModel::alias('fs')
            ->join('film f', 'fs.film_id = f.id', 'LEFT')
            ->field([
                'fs.*',
                'f.film_name',
                'f.film_type',
                'f.film_cover',
                'f.file_introduce'
            ])
            ->where('fs.id', $fileSessionId)
            ->find();
        
        if (!$schedule) {
            $this->error('排期信息不存在');
        }
        
        $this->success('获取成功', $schedule);
    }

    /**
     * 按日期查询影片排期
     * 
     * @ApiTitle    (按日期查询排期)
     * @ApiSummary  (根据日期和状态查询影片排期列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/selectByFilmArrangedDate)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="filmArrangedDate", type="string", required=true, description="影片安排日期")
     * @ApiParams   (name="filmState", type="string", required=true, description="影片状态")
     */
    public function selectByFilmArrangedDate()
    {
        $filmArrangedDate = $this->request->param('filmArrangedDate');
        $filmState = $this->request->param('filmState');
        
        if (!$filmArrangedDate || !$filmState) {
            $this->error('参数错误');
        }
        
        $schedule = FilmSessionModel::alias('fs')
            ->join('film f', 'fs.film_id = f.id', 'LEFT')
            ->field([
                'fs.*',
                'f.film_name',
                'f.film_type',
                'f.film_cover'
            ])
            ->where('fs.film_arranged_date', $filmArrangedDate)
            ->where('fs.film_state', $filmState)
            ->where('fs.del_flag', '0')
            ->order('fs.film_start_time asc')
            ->select();
        
        $this->success('获取成功', $schedule);
    }

    /**
     * 影片预约
     * 
     * @ApiTitle    (影片预约)
     * @ApiSummary  (用户预约影片场次)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/fileSession/reservations)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     * @ApiParams   (name="linkmanIds", type="array", required=true, description="联系人ID列表")
     */
    public function reservations()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            $this->error('用户未登录', null, 401);
        }
        
        $filmSessionId = $this->request->param('filmSessionId');
        $linkmanIds = $this->request->param('linkmanIds');
        
        if (!$filmSessionId || !$linkmanIds || !is_array($linkmanIds)) {
            $this->error('参数错误');
        }
        
        // 检查影片场次是否存在且可预约
        $filmSession = FilmSessionModel::where('id', $filmSessionId)
            ->where('del_flag', '0')
            ->where('film_state', '1')
            ->find();
        
        if (!$filmSession) {
            $this->error('影片场次不存在或不可预约');
        }
        
        // 检查是否已预约
        $existCount = FilmSubscribe::where('film_seeion_id', $filmSessionId)
            ->where('user_id', $userId)
            ->where('del_flag', '0')
            ->count();
        
        if ($existCount > 0) {
            $this->error('您已预约过该场次');
        }
        
        // 生成批次号
        $batchNumber = date('YmdHis') . Random::numeric(6);
        
        // 批量插入预约记录
        $insertData = [];
        foreach ($linkmanIds as $linkmanId) {
            $insertData[] = [
                'film_seeion_id' => $filmSessionId,
                'user_id' => $userId,
                'user_linkman_id' => $linkmanId,
                'batch_number' => $batchNumber,
                'subscribe_time' => date('Y-m-d H:i:s'),
                'subscribe_state' => '1', // 1表示已预约
                'del_flag' => '0',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
        }
        
        $result = (new FilmSubscribe())->insertAll($insertData);
        
        if ($result) {
            // 更新已预约人数
            FilmSessionModel::where('id', $filmSessionId)
                ->setInc('inventory_votes', count($linkmanIds));
            
            $this->success('预约成功', $batchNumber);
        } else {
            $this->error('预约失败');
        }
    }

    /**
     * 取消影片预约
     * 
     * @ApiTitle    (取消预约)
     * @ApiSummary  (取消指定人员的影片预约)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/cancelFilmSession)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     * @ApiParams   (name="vote", type="integer", required=true, description="票数")
     * @ApiParams   (name="peopleIds", type="array", required=false, description="人员ID列表")
     */
    public function cancelFilmSession()
    {
        $batchNumber = $this->request->param('batchNumber');
        $filmSessionId = $this->request->param('filmSessionId');
        $vote = $this->request->param('vote');
        $peopleIds = $this->request->param('peopleIds', []);
        
        if (!$batchNumber || !$filmSessionId || !$vote) {
            $this->error('参数错误');
        }
        
        $where = [
            'batch_number' => $batchNumber,
            'film_seeion_id' => $filmSessionId,
            'del_flag' => '0'
        ];
        
        if (!empty($peopleIds)) {
            $where['user_linkman_id'] = ['in', $peopleIds];
        }
        
        // 软删除预约记录
        $result = FilmSubscribe::where($where)
            ->update([
                'del_flag' => '2',
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            // 减少已预约人数
            FilmSessionModel::where('id', $filmSessionId)
                ->setDec('inventory_votes', $vote);
            
            $this->success('取消成功', $result);
        } else {
            $this->error('取消失败');
        }
    }

    /**
     * 个人中心影片列表
     * 
     * @ApiTitle    (个人影片列表)
     * @ApiSummary  (查询当前用户的影片预约历史)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/personalCenterFilm)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function personalCenterFilm()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            $this->error('用户未登录', null, 401);
        }
        
        // 查询用户的影片预约历史
        $list = FilmSubscribe::alias('fs')
            ->join('film_session fss', 'fs.film_seeion_id = fss.id', 'LEFT')
            ->join('film f', 'fss.film_id = f.id', 'LEFT')
            ->join('user_linkman ul', 'fs.user_linkman_id = ul.id', 'LEFT')
            ->field([
                'fs.*',
                'fss.film_start_time',
                'fss.film_end_time',
                'fss.film_arranged_date',
                'fss.year',
                'fss.week',
                'fss.inventory_votes',
                'fss.film_poll',
                'f.film_name',
                'f.film_type',
                'f.film_time',
                'f.file_introduce',
                'f.film_cover',
                'f.id as film_id',
                'ul.linkman_name'
            ])
            ->where('fs.user_id', $userId)
            ->where('fs.del_flag', '0')
            ->order('fs.subscribe_time desc')
            ->select();
        
        // 查询批次号总数
        $batchNumbers = FilmSubscribe::where('user_id', $userId)
            ->where('del_flag', '0')
            ->distinct(true)
            ->column('batch_number');
        
        $total = count($batchNumbers);
        
        $result = [
            'total' => $total,
            'rows' => $list
        ];
        
        $this->success('获取成功', $result);
    }

    /**
     * 根据场次ID获取个人影片信息
     * 
     * @ApiTitle    (个人影片详情)
     * @ApiSummary  (根据场次ID查询个人中心影片详情)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/personalCenterFilmByFilmSessionId)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     */
    public function personalCenterFilmByFilmSessionId()
    {
        $filmSessionId = $this->request->param('filmSessionId');
        
        if (!$filmSessionId) {
            $this->error('参数错误');
        }
        
        $filmInfo = FilmSubscribe::alias('fs')
            ->join('film_session fss', 'fs.film_seeion_id = fss.id', 'LEFT')
            ->join('film f', 'fss.film_id = f.id', 'LEFT')
            ->field([
                'fs.*',
                'fss.film_start_time',
                'fss.film_end_time',
                'fss.film_arranged_date',
                'fss.year',
                'fss.week',
                'fss.inventory_votes',
                'fss.film_poll',
                'f.film_name',
                'f.film_type',
                'f.film_time',
                'f.file_introduce',
                'f.film_cover',
                'f.id as film_id'
            ])
            ->where('fs.film_seeion_id', $filmSessionId)
            ->where('fs.del_flag', '0')
            ->find();
        
        if (!$filmInfo) {
            $this->error('影片信息不存在');
        }
        
        $this->success('获取成功', $filmInfo);
    }

    /**
     * 获取预约人员列表
     * 
     * @ApiTitle    (预约人员列表)
     * @ApiSummary  (根据批次号查询预约的人员信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/getFilmSubscribePeoples)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function getFilmSubscribePeoples()
    {
        $batchNumber = $this->request->param('batchNumber');
        
        if (!$batchNumber) {
            $this->error('参数错误');
        }
        
        $peoples = FilmSubscribe::alias('fs')
            ->join('user_linkman ul', 'fs.user_linkman_id = ul.id', 'LEFT')
            ->field([
                'fs.*',
                'ul.linkman_name',
                'ul.linkman_phone',
                'ul.linkman_certificate',
                'ul.linkman_age'
            ])
            ->where('fs.batch_number', $batchNumber)
            ->where('fs.del_flag', '0')
            ->select();
        
        $this->success('操作成功', $peoples);
    }

    /**
     * 检查是否已检票
     * 
     * @ApiTitle    (检查检票状态)
     * @ApiSummary  (检查指定批次是否已经检票)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/isCheck)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function isCheck()
    {
        $batchNumber = $this->request->param('batchNumber');
        
        if (!$batchNumber) {
            $this->error('参数错误');
        }
        
        $subscribe = FilmSubscribe::where('batch_number', $batchNumber)
            ->where('del_flag', '0')
            ->find();
        
        if (!$subscribe) {
            $this->error('预约信息不存在');
        }
        
        $checkStatus = $subscribe['subscribe_state'] == '3' ? '1' : '0'; // 3表示已检票
        
        $this->success('操作成功', $checkStatus);
    }

    /**
     * 执行检票操作
     * 
     * @ApiTitle    (执行检票)
     * @ApiSummary  (对指定批次执行检票操作)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/check)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function check()
    {
        $batchNumber = $this->request->param('batchNumber');
        
        if (!$batchNumber) {
            $this->error('参数错误');
        }
        
        $result = FilmSubscribe::where('batch_number', $batchNumber)
            ->where('del_flag', '0')
            ->update([
                'subscribe_state' => '3', // 3表示已检票
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            $this->success('检票成功', $result);
        } else {
            $this->error('检票失败');
        }
    }

    /**
     * 检查黑名单状态
     * 
     * @ApiTitle    (检查黑名单)
     * @ApiSummary  (检查当前用户是否在黑名单中)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/isBalckList)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function isBalckList()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            $this->error('用户未登录', null, 401);
        }
        
        $blackList = FilmBlackList::where('user_id', $userId)
            ->where('del_flag', '0')
            ->find();
        
        if (!$blackList) {
            $this->success('操作成功', false);
        } else {
            $this->success('操作成功', $blackList['create_time']);
        }
    }

    /**
     * 获取二维码信息
     * 
     * @ApiTitle    (获取二维码)
     * @ApiSummary  (生成影片预约的二维码信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/qrCodeInfo)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     * @ApiParams   (name="filmType", type="string", required=true, description="影片类型")
     */
    public function qrCodeInfo()
    {
        $batchNumber = $this->request->param('batchNumber');
        $filmType = $this->request->param('filmType');
        
        if (!$batchNumber || !$filmType) {
            $this->error('参数错误');
        }
        
        try {
            // 引入DES加密类
            $des = new \app\common\library\DesEncryption();
            
            // 查询预约信息
            $subscribes = FilmSubscribe::alias('fs')
                ->join('user_linkman ul', 'fs.user_linkman_id = ul.id', 'LEFT')
                ->join('film_session fss', 'fs.film_seeion_id = fss.id', 'LEFT')
                ->field([
                    'fs.*',
                    'ul.linkman_name',
                    'ul.linkman_certificate',
                    'fss.film_start_time',
                    'fss.film_end_time'
                ])
                ->where('fs.batch_number', $batchNumber)
                ->where('fs.del_flag', '0')
                ->select();
            
            if (!$subscribes) {
                $this->error('预约信息不存在');
            }
            
            $qrCodes = [];
            foreach ($subscribes as $subscribe) {
                // 生成二维码数据
                $qrData = [
                    'id' => $subscribe['id'],
                    'filmSeeionId' => $subscribe['film_seeion_id'],
                    'userId' => $subscribe['user_id'],
                    'name' => $subscribe['linkman_name'],
                    'sfid' => $subscribe['linkman_certificate'],
                    'showTime' => $subscribe['film_start_time'],
                    'time' => time(),
                    'code' => md5($subscribe['id'] . time()),
                    'codeType' => '1'
                ];
                
                // 使用与Java端一致的DES加密
                $encryptedData = \app\common\library\DesEncryption::generateQrCode($qrData);
                
                $qrCodes[] = [
                    'id' => $subscribe['id'],
                    'name' => $subscribe['linkman_name'],
                    'qrCode' => $encryptedData
                ];
            }
            
            $this->success('操作成功', $qrCodes);
        } catch (\Exception $e) {
            $this->error('加密数据失败,请联系管理员');
        }
    }

    /**
     * 二维码验证
     * 
     * @ApiTitle    (二维码验证)
     * @ApiSummary  (解密并验证二维码，进行签到操作)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/fileSession/verify)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="data", type="string", required=true, description="加密的二维码数据")
     */
    public function verify()
    {
        try {
            $data = $this->request->param('data');
            
            if (!$data) {
                $this->error('参数错误');
            }
            
            // 使用与Java端一致的DES解密
            $content = \app\common\library\DesEncryption::parseQrCode($data);
            
            if (!\app\common\library\DesEncryption::validateQrData($content)) {
                $this->error('二维码格式错误');
            }
            
            $sfid = $content['sfid'] ?? '';
            $name = $content['name'] ?? '';
            $showTime = $content['showTime'] ?? '';
            $codeType = $content['codeType'] ?? '';
            
            if ($codeType != '1') {
                $this->error('非观影二维码');
            }
            
            if (empty($sfid) || empty($name) || empty($showTime)) {
                $this->error('签到失败，信息不全');
            }
            
            $id = $content['id'] ?? 0;
            $filmSeeionId = $content['filmSeeionId'] ?? 0;
            $userId = $content['userId'] ?? 0;
            
            if ($id && $filmSeeionId && $userId) {
                // 直接验证模式
                $filmSession = FilmSessionModel::where('id', $filmSeeionId)->find();
                $filmSubscribe = FilmSubscribe::where('id', $id)->find();
                
                if (!$filmSession || !$filmSubscribe) {
                    $this->error('未找到预约信息');
                }
                
                $curFilmSession = $filmSession;
                $curFilmSubscribe = $filmSubscribe;
            } else {
                // 通过姓名和身份证查找
                $linkman = UserLinkman::where('linkman_name', $name)
                    ->where('linkman_certificate', $sfid)
                    ->find();
                
                if (!$linkman) {
                    $this->error('未找到联系人信息');
                }
                
                // 查找当天的预约记录
                $today = date('Y-m-d');
                $subscribes = FilmSubscribe::alias('fs')
                    ->join('film_session fss', 'fs.film_seeion_id = fss.id', 'LEFT')
                    ->where('fs.user_linkman_id', $linkman['id'])
                    ->where('fs.del_flag', '0')
                    ->where('fss.film_arranged_date', $today)
                    ->select();
                
                if (!$subscribes) {
                    $this->error('未找到预约信息');
                }
                
                // 找到最近的场次
                $curFilmSubscribe = $subscribes[0];
                $curFilmSession = FilmSessionModel::where('id', $curFilmSubscribe['film_seeion_id'])->find();
            }
            
            if ($curFilmSubscribe['subscribe_state'] != '4') {
                $this->error('非已签到状态');
            }
            
            // 检查时间
            $startTime = strtotime($curFilmSession['film_start_time']);
            $currentTime = time();
            $timeDiff = ($startTime - $currentTime) / 60; // 分钟差
            
            if ($timeDiff > 10) {
                $this->error('影片未开始');
            }
            
            // 验证成功，更新状态
            FilmSubscribe::where('id', $curFilmSubscribe['id'])
                ->update([
                    'subscribe_state' => '5', // 5表示已验证
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            
            $info = [
                'name' => $name,
                'sfid' => $sfid,
                'startTime' => $curFilmSession['film_start_time'],
                'endTime' => $curFilmSession['film_end_time'],
                'filmSeeionId' => $curFilmSession['id'],
                'subscribeId' => $curFilmSubscribe['id'],
                'linkId' => $curFilmSubscribe['user_linkman_id'],
                'totalPoll' => $curFilmSession['film_poll'],
                'inventoryVotes' => $curFilmSession['inventory_votes']
            ];
            
            $this->success('操作成功', $info);
        } catch (\Exception $e) {
            $this->error('后台处理失败，数据异常');
        }
    }

    /**
     * 个人预约统计
     * 
     * @ApiTitle    (预约统计)
     * @ApiSummary  (查询用户的场馆预约统计信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/subscribeEstimate)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function subscribeEstimate()
    {
        $userId = $this->getUserId();
        if (!$userId) {
            $this->error('用户未登录', null, 401);
        }
        
        // 查询用户的预约统计
        $estimates = FilmSubscribe::alias('fs')
            ->join('film_session fss', 'fs.film_seeion_id = fss.id', 'LEFT')
            ->join('film f', 'fss.film_id = f.id', 'LEFT')
            ->join('venue v', 'fss.venue_id = v.id', 'LEFT')
            ->field([
                'COUNT(*) as total_count',
                'v.venue_name',
                'f.film_type'
            ])
            ->where('fs.user_id', $userId)
            ->where('fs.del_flag', '0')
            ->group('v.id, f.film_type')
            ->select();
        
        $this->success('获取成功', $estimates);
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }
}