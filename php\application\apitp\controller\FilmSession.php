<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\FilmSession as FilmSessionModel;
use app\common\model\FilmSubscribe;
use app\common\model\FilmBlackList;
use app\common\model\UserLinkman;
use app\common\model\WebUser;
use think\Request;
use think\Cache;
use think\Validate;
use think\exception\ValidateException;
use think\Db;
use fast\Random;

/**
 * 影片场次控制器
 * @ApiWeigh (98)
 */
class FilmSession extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

   

  

    /**
     * 个人中心影片信息
     * 
     * @ApiTitle    (个人中心影片信息)
     * @ApiSummary  (获取用户个人中心的影片预订信息列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/personalCenterFilm)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function personalCenterFilm()
    {  
        $userId = $this->getUserId();
        // 查询用户的影片预订信息
        $personalCenterFilms = FilmSubscribe::alias('fs')
            ->join(['film_session'=>'fss'], 'fs.film_seeion_id = fss.id', 'LEFT')
            ->join(['film'=>'f'], 'fss.film_id = f.id', 'LEFT')
            ->field([
                'f.film_type as filmType',
                'f.film_name as filmName', 
                'CONCAT(DATE_FORMAT(fss.film_start_time,\'%H:%i:%s\'),\'-\',DATE_FORMAT(fss.film_end_time,\'%H:%i:%s\')) as filmTime',
                'f.file_introduce as fileIntroduce',
                'f.film_cover as filmCover',
                'f.id as filmId',
                'fss.id as filmSeeionId',
                'DATE_FORMAT(fss.film_start_time,\'%Y/%m/%d %H:%i:%s\') as filmStartTime',
                'DATE_FORMAT(fss.film_end_time,\'%Y/%m/%d %H:%i:%s\') as filmEndTime', 
                'DATE_FORMAT(fss.film_arranged_date,\'%Y/%m/%d\') as filmArrangedDate',
                'fss.year as year',
                'fss.week as week',
                'fss.inventory_votes as inventoryVotes',
                'fss.film_poll as filmPoll',
                'fs.subscribe_state as subscribeState',
                'fs.subscribe_type as subscribeType',
                'fs.batch_number as batchNumber',
                'IF(fss.inventory_votes > 0, \'1\', \'0\') as isSurplus',
                'fs.is_ticket as isTicket'
            ])
            ->where('fs.user_id', $userId)
            ->where('fs.del_flag', '0')
            ->order('fss.film_start_time desc')
            ->select();
        // 查询用户的批次号列表用于统计总数
        $batchNumbers = FilmSubscribe::where('user_id', $userId)
            ->where('del_flag', '0')
            ->column('batch_number');
            
        $total = count(array_unique($batchNumbers));
        
        $result = [
            'total' => $total,
            'rows' => $personalCenterFilms
        ];
        
        $this->success('获取成功', $result);
    }
    

    /**
     * 获取影片预约人员信息
     * 
     * @ApiTitle    (获取影片预约人员信息)
     * @ApiSummary  (根据批次号获取影片预约的观影人员信息列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/getFilmSubscribePeoples)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function getFilmSubscribePeoples()
    {
        $batchNumber = $this->request->param('batchNumber');
        
        // 参数验证
        if (empty($batchNumber)) {
            $this->error('批次号不能为空');
        }
        
      
        // 查询观影人员信息
        $peoples = FilmSubscribe::alias('c')
            ->join(['user_linkman' => 'l'], 'l.id = c.user_linkman_id AND l.del_flag = "0"', 'INNER')
            ->field([
                'c.user_linkman_id as linkId',
                'l.linkman_name as linkmanName', 
                'l.linkman_phone as linkmanPhone',
                'l.linkman_age as linkmanAge',
                'l.linkman_certificate as linkmanCertificate',
                '1 as linkCheck'  // 固定返回true，
            ])
            ->where('c.batch_number', $batchNumber)
            ->where('c.subscribe_state', '<>', '3')  // 排除已取消的预约
            ->where('c.del_flag', '0')
            ->select();
        // 转换linkCheck为布尔值
        foreach ($peoples as &$people) {
            $people['linkCheck'] = true;
        }
        
        $this->success('操作成功', $peoples);
      
    }

    /**
     * 取消影片预约
     * 
     * @ApiTitle    (取消影片预约)
     * @ApiSummary  (取消用户的影片预约，支持取消整个批次或指定人员)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/cancelFilmSession)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     * @ApiParams   (name="vote", type="integer", required=true, description="票数/人数")
     * @ApiParams   (name="peopleIds", type="array", required=false, description="人员ID列表，指定要取消的具体人员")
     */
    public function cancelFilmSession()
    {
        $batchNumber = $this->request->param('batchNumber');
        $filmSessionId = $this->request->param('filmSessionId');
        $vote = $this->request->param('vote');
        $peopleIds = $this->request->param('peopleIds', []);
        
        // 参数验证
        if (empty($batchNumber)) {
            $this->error('批次号不能为空');
        }
        if (empty($filmSessionId)) {
            $this->error('影片场次ID不能为空');
        }
        if (empty($vote) || $vote == 0) {
            $this->error('票数有误');
        }
        
    
        Db::startTrans();
        
        $cancelNum = $vote;
        if (!empty($peopleIds) && $vote > count($peopleIds)) {
            $cancelNum = count($peopleIds);
        }
        
        // 获取影片场次信息
        $filmSession = FilmSessionModel::where('id', $filmSessionId)->find();
        if (!$filmSession) {
            $this->error('影片场次不存在');
        }
        
        // 检查时间限制（开场前10分钟不允许取消）
        $filmStartTime = strtotime($filmSession['film_start_time']);
        $checkTime = $filmStartTime - (10 * 60); // 开场前10分钟
        if (time() >= $checkTime) {
            $this->error('开场时间前10分钟不允许取消');
        }
        
        // 检查是否已有检票记录
        $hasTicket = FilmSubscribe::where('batch_number', $batchNumber)
            ->where('is_ticket', '1')
            ->count();
        if ($hasTicket > 0) {
            $this->error('已有检票,不能取消');
        }
        
        // 更新库存票数
        $currentInventory = $filmSession['inventory_votes'];
        $newInventory = $currentInventory + $cancelNum;
        FilmSessionModel::where('id', $filmSessionId)
            ->update(['inventory_votes' => $newInventory]);
        
        // 更新预约类型
        $newSubscribeType = $vote - $cancelNum;
        FilmSubscribe::where('batch_number', $batchNumber)
            ->update(['subscribe_type' => $newSubscribeType]);
        
        // 取消预约记录
        $affectedRows = 0;
        if (!empty($peopleIds)) {
            // 取消指定人员的预约
            foreach ($peopleIds as $linkId) {
                $result = FilmSubscribe::where('batch_number', $batchNumber)
                    ->where('user_linkman_id', $linkId)
                    ->delete();
                if ($result > 0) {
                    $affectedRows += $result;
                } else {
                    Db::rollback();
                    $this->error('批次号有误');
                }
            }
        } else {
            // 取消整个批次的预约
            $affectedRows = FilmSubscribe::where('batch_number', $batchNumber)->delete();
        }
        
        Db::commit();
        $this->success('取消成功', $affectedRows);
      
    }

    /**
     * 通过场次ID获取个人中心影片信息
     * @ApiTitle    (通过场次ID获取个人中心影片信息)
     * @ApiSummary  (根据影片场次ID获取详细的影片和场次信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/personalCenterFilmByFilmSessionId)
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     */
    public function personalCenterFilmByFilmSessionId()
    {
        // 获取影片场次ID参数
        $filmSessionId = $this->request->param('filmSessionId', 0, 'intval');

        if (!$filmSessionId) {
            return $this->error('影片场次ID不能为空');
        }

      
        // 查询影片场次详细信息
        $filmInfo = $this->getFilmSessionDetail($filmSessionId);

        if (!$filmInfo) {
            return $this->error('未找到对应的影片场次信息');
        }

        return $this->success('获取成功', $filmInfo);

    }

    /**
     * 获取影片场次详细信息
     * @param int $filmSessionId 影片场次ID
     * @return array|null
     */
    private function getFilmSessionDetail($filmSessionId)
    {
        // 查询影片和场次信息
        $filmInfo = \app\common\model\FilmSession::alias('fs')
            ->join(['film' => 'f'], 'f.id = fs.film_id')
            ->field([
                'f.film_type as filmType',
                'f.film_name as filmName',
                'f.file_introduce as fileIntroduce',
                'f.film_cover as filmCover',
                'f.id as filmId',
                'fs.id as filmSeeionId',
                'fs.film_start_time as filmStartTime',
                'fs.film_end_time as filmEndTime',
                'fs.film_arranged_date as filmArrangedDate',
                'fs.year',
                'fs.week',
                'fs.inventory_votes as inventoryVotes',
                'fs.film_poll as filmPoll'
            ])
            ->where('fs.id', $filmSessionId)
            ->find();

        if (!$filmInfo) {
            return null;
        }

        // 格式化时间字段
        if ($filmInfo['filmStartTime']) {
            $filmInfo['filmStartTime'] = date('Y/m/d H:i:s', strtotime($filmInfo['filmStartTime']));
        }

        if ($filmInfo['filmEndTime']) {
            $filmInfo['filmEndTime'] = date('Y/m/d H:i:s', strtotime($filmInfo['filmEndTime']));
        }

        if ($filmInfo['filmArrangedDate']) {
            $filmInfo['filmArrangedDate'] = date('Y/m/d', strtotime($filmInfo['filmArrangedDate']));
        }

        // 构造影片时间字段 (开始时间-结束时间)
        if ($filmInfo['filmStartTime'] && $filmInfo['filmEndTime']) {
            $startTime = date('H:i:s', strtotime($filmInfo['filmStartTime']));
            $endTime = date('H:i:s', strtotime($filmInfo['filmEndTime']));
            $filmInfo['filmTime'] = $startTime . '-' . $endTime;
        }

        // 设置默认值（这些字段在当前查询中不包含，但Java版本的VO对象包含）
        $filmInfo['subscribeState'] = '';
        $filmInfo['subscribeType'] = '';
        $filmInfo['batchNumber'] = '';
        $filmInfo['isSurplus'] = '';
        $filmInfo['isTicket'] = '';

        return $filmInfo;
    }

    /**
     * 检查批次号的预约状态
     * @ApiTitle    (检查批次号的预约状态)
     * @ApiSummary  (通过批次号查询判断用户是否签到)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/isCheck)
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function isCheck()
    {
        // 获取批次号参数
        $batchNumber = $this->request->param('batchNumber', '');

        // 参数验证
        if (empty($batchNumber)) {
            return $this->error('批次号不能为空');
        }

       
        // 查询批次号的预约状态
        $subscribeState = $this->getSubscribeStateByBatchNumber($batchNumber);

        return $this->success('操作成功', $subscribeState);

    }

    /**
     * 通过批次号查询预约状态
     * @param string $batchNumber 批次号
     * @return string|null
     */
    private function getSubscribeStateByBatchNumber($batchNumber)
    {
        // 查询该批次号的预约状态
        $subscribeState = FilmSubscribe::where('batch_number', $batchNumber)
            ->value('subscribe_state');

        return $subscribeState ?: '';
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }
}