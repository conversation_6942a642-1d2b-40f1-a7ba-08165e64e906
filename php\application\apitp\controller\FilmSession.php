<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\FilmSession as FilmSessionModel;
use app\common\model\FilmSubscribe;
use app\common\model\FilmBlackList;
use app\common\model\UserLinkman;
use app\common\model\WebUser;
use think\Request;
use think\Cache;
use think\Validate;
use think\exception\ValidateException;
use think\Db;
use fast\Random;

/**
 * 影片场次控制器
 * @ApiWeigh (98)
 */
class FilmSession extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

   

  

    /**
     * 个人中心影片信息
     * 
     * @ApiTitle    (个人中心影片信息)
     * @ApiSummary  (获取用户个人中心的影片预订信息列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/personalCenterFilm)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function personalCenterFilm()
    {  
        $userId = $this->getUserId();
        // 查询用户的影片预订信息
        $personalCenterFilms = FilmSubscribe::alias('fs')
            ->join(['film_session'=>'fss'], 'fs.film_seeion_id = fss.id', 'LEFT')
            ->join(['film'=>'f'], 'fss.film_id = f.id', 'LEFT')
            ->field([
                'f.film_type as filmType',
                'f.film_name as filmName', 
                'CONCAT(DATE_FORMAT(fss.film_start_time,\'%H:%i:%s\'),\'-\',DATE_FORMAT(fss.film_end_time,\'%H:%i:%s\')) as filmTime',
                'f.file_introduce as fileIntroduce',
                'f.film_cover as filmCover',
                'f.id as filmId',
                'fss.id as filmSeeionId',
                'DATE_FORMAT(fss.film_start_time,\'%Y/%m/%d %H:%i:%s\') as filmStartTime',
                'DATE_FORMAT(fss.film_end_time,\'%Y/%m/%d %H:%i:%s\') as filmEndTime', 
                'DATE_FORMAT(fss.film_arranged_date,\'%Y/%m/%d\') as filmArrangedDate',
                'fss.year as year',
                'fss.week as week',
                'fss.inventory_votes as inventoryVotes',
                'fss.film_poll as filmPoll',
                'fs.subscribe_state as subscribeState',
                'fs.subscribe_type as subscribeType',
                'fs.batch_number as batchNumber',
                'IF(fss.inventory_votes > 0, \'1\', \'0\') as isSurplus',
                'fs.is_ticket as isTicket'
            ])
            ->where('fs.user_id', $userId)
            ->where('fs.del_flag', '0')
            ->order('fss.film_start_time desc')
            ->select();
        // 查询用户的批次号列表用于统计总数
        $batchNumbers = FilmSubscribe::where('user_id', $userId)
            ->where('del_flag', '0')
            ->column('batch_number');
            
        $total = count(array_unique($batchNumbers));
        
        $result = [
            'total' => $total,
            'rows' => $personalCenterFilms
        ];
        
        $this->success('获取成功', $result);
    }
    

    /**
     * 获取影片预约人员信息
     * 
     * @ApiTitle    (获取影片预约人员信息)
     * @ApiSummary  (根据批次号获取影片预约的观影人员信息列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/getFilmSubscribePeoples)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function getFilmSubscribePeoples()
    {
        $batchNumber = $this->request->param('batchNumber');
        
        // 参数验证
        if (empty($batchNumber)) {
            $this->error('批次号不能为空');
        }
        
      
        // 查询观影人员信息
        $peoples = FilmSubscribe::alias('c')
            ->join(['user_linkman' => 'l'], 'l.id = c.user_linkman_id AND l.del_flag = "0"', 'INNER')
            ->field([
                'c.user_linkman_id as linkId',
                'l.linkman_name as linkmanName', 
                'l.linkman_phone as linkmanPhone',
                'l.linkman_age as linkmanAge',
                'l.linkman_certificate as linkmanCertificate',
                '1 as linkCheck'  // 固定返回true，
            ])
            ->where('c.batch_number', $batchNumber)
            ->where('c.subscribe_state', '<>', '3')  // 排除已取消的预约
            ->where('c.del_flag', '0')
            ->select();
        // 转换linkCheck为布尔值
        foreach ($peoples as &$people) {
            $people['linkCheck'] = true;
        }
        
        $this->success('操作成功', $peoples);
      
    }

    /**
     * 取消影片预约
     * 
     * @ApiTitle    (取消影片预约)
     * @ApiSummary  (取消用户的影片预约，支持取消整个批次或指定人员)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/cancelFilmSession)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     * @ApiParams   (name="vote", type="integer", required=true, description="票数/人数")
     * @ApiParams   (name="peopleIds", type="array", required=false, description="人员ID列表，指定要取消的具体人员")
     */
    public function cancelFilmSession()
    {
        $batchNumber = $this->request->param('batchNumber');
        $filmSessionId = $this->request->param('filmSessionId');
        $vote = $this->request->param('vote');
        $peopleIds = $this->request->param('peopleIds', []);
        
        // 参数验证
        if (empty($batchNumber)) {
            $this->error('批次号不能为空');
        }
        if (empty($filmSessionId)) {
            $this->error('影片场次ID不能为空');
        }
        if (empty($vote) || $vote == 0) {
            $this->error('票数有误');
        }
        
    
        Db::startTrans();
        
        $cancelNum = $vote;
        if (!empty($peopleIds) && $vote > count($peopleIds)) {
            $cancelNum = count($peopleIds);
        }
        
        // 获取影片场次信息
        $filmSession = FilmSessionModel::where('id', $filmSessionId)->find();
        if (!$filmSession) {
            $this->error('影片场次不存在');
        }
        
        // 检查时间限制（开场前10分钟不允许取消）
        $filmStartTime = strtotime($filmSession['film_start_time']);
        $checkTime = $filmStartTime - (10 * 60); // 开场前10分钟
        if (time() >= $checkTime) {
            $this->error('开场时间前10分钟不允许取消');
        }
        
        // 检查是否已有检票记录
        $hasTicket = FilmSubscribe::where('batch_number', $batchNumber)
            ->where('is_ticket', '1')
            ->count();
        if ($hasTicket > 0) {
            $this->error('已有检票,不能取消');
        }
        
        // 更新库存票数
        $currentInventory = $filmSession['inventory_votes'];
        $newInventory = $currentInventory + $cancelNum;
        FilmSessionModel::where('id', $filmSessionId)
            ->update(['inventory_votes' => $newInventory]);
        
        // 更新预约类型
        $newSubscribeType = $vote - $cancelNum;
        FilmSubscribe::where('batch_number', $batchNumber)
            ->update(['subscribe_type' => $newSubscribeType]);
        
        // 取消预约记录
        $affectedRows = 0;
        if (!empty($peopleIds)) {
            // 取消指定人员的预约
            foreach ($peopleIds as $linkId) {
                $result = FilmSubscribe::where('batch_number', $batchNumber)
                    ->where('user_linkman_id', $linkId)
                    ->delete();
                if ($result > 0) {
                    $affectedRows += $result;
                } else {
                    Db::rollback();
                    $this->error('批次号有误');
                }
            }
        } else {
            // 取消整个批次的预约
            $affectedRows = FilmSubscribe::where('batch_number', $batchNumber)->delete();
        }
        
        Db::commit();
        $this->success('取消成功', $affectedRows);
      
    }

    /**
     * 通过场次ID获取个人中心影片信息
     * @ApiTitle    (通过场次ID获取个人中心影片信息)
     * @ApiSummary  (根据影片场次ID获取详细的影片和场次信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/personalCenterFilmByFilmSessionId)
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     */
    public function personalCenterFilmByFilmSessionId()
    {
        // 获取影片场次ID参数
        $filmSessionId = $this->request->param('filmSessionId', 0, 'intval');

        if (!$filmSessionId) {
            return $this->error('影片场次ID不能为空');
        }

      
        // 查询影片场次详细信息
        $filmInfo = $this->getFilmSessionDetail($filmSessionId);

        if (!$filmInfo) {
            return $this->error('未找到对应的影片场次信息');
        }

        return $this->success('获取成功', $filmInfo);

    }

    /**
     * 获取影片场次详细信息
     * @param int $filmSessionId 影片场次ID
     * @return array|null
     */
    private function getFilmSessionDetail($filmSessionId)
    {
        // 查询影片和场次信息
        $filmInfo = \app\common\model\FilmSession::alias('fs')
            ->join(['film' => 'f'], 'f.id = fs.film_id')
            ->field([
                'f.film_type as filmType',
                'f.film_name as filmName',
                'f.file_introduce as fileIntroduce',
                'f.film_cover as filmCover',
                'f.id as filmId',
                'fs.id as filmSeeionId',
                'fs.film_start_time as filmStartTime',
                'fs.film_end_time as filmEndTime',
                'fs.film_arranged_date as filmArrangedDate',
                'fs.year',
                'fs.week',
                'fs.inventory_votes as inventoryVotes',
                'fs.film_poll as filmPoll'
            ])
            ->where('fs.id', $filmSessionId)
            ->find();

        if (!$filmInfo) {
            return null;
        }

        // 格式化时间字段
        if ($filmInfo['filmStartTime']) {
            $filmInfo['filmStartTime'] = date('Y/m/d H:i:s', strtotime($filmInfo['filmStartTime']));
        }

        if ($filmInfo['filmEndTime']) {
            $filmInfo['filmEndTime'] = date('Y/m/d H:i:s', strtotime($filmInfo['filmEndTime']));
        }

        if ($filmInfo['filmArrangedDate']) {
            $filmInfo['filmArrangedDate'] = date('Y/m/d', strtotime($filmInfo['filmArrangedDate']));
        }

        // 构造影片时间字段 (开始时间-结束时间)
        if ($filmInfo['filmStartTime'] && $filmInfo['filmEndTime']) {
            $startTime = date('H:i:s', strtotime($filmInfo['filmStartTime']));
            $endTime = date('H:i:s', strtotime($filmInfo['filmEndTime']));
            $filmInfo['filmTime'] = $startTime . '-' . $endTime;
        }

        // 设置默认值（这些字段在当前查询中不包含，但Java版本的VO对象包含）
        $filmInfo['subscribeState'] = '';
        $filmInfo['subscribeType'] = '';
        $filmInfo['batchNumber'] = '';
        $filmInfo['isSurplus'] = '';
        $filmInfo['isTicket'] = '';

        return $filmInfo;
    }

    /**
     * 检查批次号的预约状态
     * @ApiTitle    (检查批次号的预约状态)
     * @ApiSummary  (通过批次号查询判断用户是否签到)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/isCheck)
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function isCheck()
    {
        // 获取批次号参数
        $batchNumber = $this->request->param('batchNumber', '');

        // 参数验证
        if (empty($batchNumber)) {
            return $this->error('批次号不能为空');
        }

       
        // 查询批次号的预约状态
        $subscribeState = $this->getSubscribeStateByBatchNumber($batchNumber);

        return $this->success('操作成功', $subscribeState);

    }

    /**
     * 通过批次号查询预约状态
     * @param string $batchNumber 批次号
     * @return string|null
     */
    private function getSubscribeStateByBatchNumber($batchNumber)
    {
        // 查询该批次号的预约状态
        $subscribeState = FilmSubscribe::where('batch_number', $batchNumber)
            ->value('subscribe_state');

        return $subscribeState ?: '';
    }

    /**
     * 个人场馆预约估算
     * @ApiTitle    (个人场馆预约估算)
     * @ApiSummary  (判断从当前时刻-未来的时间，是否有场馆预约，用于影视预约条件校验)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/subscribeEstimate)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function subscribeEstimate()
    {
        // 获取用户ID
        $userId = $this->getUserId();
        // 查询用户的场馆预约估算信息
        $venueSubscribeList = $this->getPersonalVenueSubscribeEstimate($userId);

        return $this->success('获取成功', $venueSubscribeList);
    }

    /**
     * 获取个人场馆预约估算数据
     * @param int $userId 用户ID
     * @return array
     */
    private function getPersonalVenueSubscribeEstimate($userId)
    {
        $currentTime = date('Y-m-d H:i:s');

        // 查询用户的场馆预约信息（场馆结束时间大于当前时间的预约）
        $venueSubscribeList = \app\common\model\VenueSubscribe::alias('vs')
            ->join(['venue' => 'v'], 'v.id = vs.venue_id')
            ->field([
                'v.id',
                'vs.user_id as userId',
                'vs.sign_state as signState',
                'v.venue_start_time as venueStartTime',
                'v.venue_end_time as venueEndTime'
            ])
            ->where('vs.user_id', $userId)
            ->where('vs.del_flag', '0')
            ->where('v.venue_end_time', '>', $currentTime)
            ->order('v.venue_start_time', 'asc')
            ->select();

        // 格式化时间字段
        foreach ($venueSubscribeList as &$item) {
            if ($item['venueStartTime']) {
                $item['venueStartTime'] = date('Y-m-d', strtotime($item['venueStartTime']));
            }
            if ($item['venueEndTime']) {
                $item['venueEndTime'] = date('Y-m-d', strtotime($item['venueEndTime']));
            }
        }

        return $venueSubscribeList;
    }

    /**
     * 检查用户是否在黑名单中
     * @ApiTitle    (检查用户是否在黑名单中)
     * @ApiSummary  (判断用户是否为黑名单用户，返回解锁时间或false)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/isBalckList)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function isBalckList()
    {
        // 获取用户ID
        $userId = $this->getUserId();

        // 查询用户是否在黑名单中
        $unlockTime = $this->checkUserBlackList($userId);

        if ($unlockTime === null) {
            // 用户不在黑名单中
            return $this->success('操作成功', false);
        }

        // 用户在黑名单中，返回解锁时间
        return $this->success('操作成功', $unlockTime);
    }

    /**
     * 检查用户黑名单状态
     * @param int $userId 用户ID
     * @return string|null 返回解锁时间或null
     */
    private function checkUserBlackList($userId)
    {
        // 查询用户黑名单记录
        $unlockTime = \app\common\model\FilmBlackList::where('user_id', $userId)
            ->where('del_flag', '0')
            ->where('is_start', '0')
            ->value('unlock_time');

        return $unlockTime;
    }

    /**
     * 影片预约
     * @ApiTitle    (影片预约)
     * @ApiSummary  (用户预约影片场次)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/fileSession/reservations)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="filmSeeionId", type="integer", required=true, description="影片场次ID")
     * @ApiParams   (name="subscribeType", type="string", required=true, description="预约票类型")
     * @ApiParams   (name="userLinkmanIdList", type="string", required=true, description="用户联系人ID列表，逗号分隔")
     * @ApiParams   (name="isSurplus", type="string", required=true, description="是否为余票(0/正常票 1/余票)")
     */
    public function reservations()
    {
        // 获取用户ID
        $userId = $this->getUserId();
        // 获取请求参数
        // $data = $this->request->post();
        // $param = json_decode($data, true);

        $param = array('filmSeeionId'=>'','subscribeType'=>'','userLinkmanIdList'=>'','isSurplus'=>'');
        // 参数验证
        if (empty($param['filmSeeionId'])) {
            return $this->error('影片场次ID不能为空');
        }
        if (empty($param['subscribeType'])) {
            return $this->error('预约票类型不能为空');
        }
        if (empty($param['userLinkmanIdList'])) {
            return $this->error('参数传递错误');
        }
        if (!isset($param['isSurplus'])) {
            return $this->error('是否为余票不能为空');
        }

        $filmSeeionId = $param['filmSeeionId'];
        $subscribeType = $param['subscribeType'];
        $userLinkmanIdList = $param['userLinkmanIdList'];
        $isSurplus = $param['isSurplus'];
     
        // 执行预约逻辑
        $batchNumber = $this->doReservations($userId, $filmSeeionId, $subscribeType, $userLinkmanIdList, $isSurplus);

        return $this->success('预约成功', $batchNumber);

    }

    /**
     * 执行影片预约逻辑
     * @param int $userId 用户ID
     * @param int $filmSeeionId 影片场次ID
     * @param string $subscribeType 预约票类型
     * @param string $userLinkmanIdList 联系人ID列表
     * @param string $isSurplus 是否余票
     * @return string 批次号
     */
    private function doReservations($userId, $filmSeeionId, $subscribeType, $userLinkmanIdList, $isSurplus)
    {
        // 检查是否重复预约
        $existCount = FilmSubscribe::where('film_seeion_id', $filmSeeionId)
            ->where('user_id', $userId)
            ->count();

        if ($existCount > 0) {
            throw new \Exception('请勿重复预约');
        }

        // 解析联系人ID列表
        $userLinkmanIds = explode(',', $userLinkmanIdList);
        $userLinkmanIds = array_filter($userLinkmanIds); // 过滤空值

        if (empty($userLinkmanIds)) {
            throw new \Exception('联系人ID列表不能为空');
        }

        // 生成批次号
        $batchNumber = $this->generateUuid();

        // 构建预约记录列表
        $filmSubscribeList = [];
        $currentTime = date('Y-m-d H:i:s');

        foreach ($userLinkmanIds as $linkmanId) {
            $filmSubscribeData = [
                'film_seeion_id' => $filmSeeionId,
                'user_id' => $userId,
                'user_linkman_id' => intval($linkmanId),
                'subscribe_type' => $subscribeType,
                'batch_number' => $batchNumber,
                'del_flag' => '0',
                'create_time' => $currentTime,
                'update_time' => $currentTime
            ];

            // 根据是否余票设置预约状态
            if ($isSurplus === '1') {
                // 余票直接设置为已签到状态
                $filmSubscribeData['subscribe_state'] = '4';
            } else {
                // 正常票，默认未签到状态
                $filmSubscribeData['subscribe_state'] = '1';

                // 检查用户是否有当天的场馆签到记录
                if ($this->checkVenueSignToday($userId)) {
                    $filmSubscribeData['subscribe_state'] = '4';
                }
            }

            $filmSubscribeList[] = $filmSubscribeData;
        }

        // 开始事务处理
        \think\Db::startTrans();
    
        // 检查并扣减库存
        $this->checkAndReduceInventory($filmSeeionId, count($userLinkmanIds));

        // 批量插入预约记录
        $result = FilmSubscribe::insertAll($filmSubscribeList);

        if (!$result) {
            throw new \Exception('预约记录插入失败');
        }

        \think\Db::commit();
        return $batchNumber;

    }

    /**
     * 检查用户是否有当天的场馆签到记录
     * @param int $userId 用户ID
     * @return bool
     */
    private function checkVenueSignToday($userId)
    {
        // 构建时间范围（当前日期-7天到当前日期）
        $currentDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime('-7 days')) . ' 00:00:00';
        $endDate = $currentDate . ' 23:59:59';

        // 查询用户在时间范围内的场馆预约记录（签到状态为1）
        $venueSubscribes = \app\common\model\VenueSubscribe::where('user_id', $userId)
            ->where('sign_state', '1')
            ->where('create_time', '>=', $startDate)
            ->where('create_time', '<=', $endDate)
            ->where('del_flag', '0')
            ->select();

        foreach ($venueSubscribes as $venueSubscribe) {
            // 获取场馆信息
            $venue = \app\common\model\Venue::where('id', $venueSubscribe['venue_id'])->find();

            if ($venue) {
                $venueStartDate = date('Y-m-d', strtotime($venue['venue_start_time']));
                // 检查是否为当天的场馆
                if ($venueStartDate === $currentDate) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查并扣减库存
     * @param int $filmSeeionId 影片场次ID
     * @param int $count 预约人数
     * @throws \Exception
     */
    private function checkAndReduceInventory($filmSeeionId, $count)
    {
        // 获取当前库存
        $filmSession = \app\common\model\FilmSession::where('id', $filmSeeionId)->find();

        if (!$filmSession) {
            throw new \Exception('影片场次不存在');
        }

        $currentInventory = $filmSession['inventory_votes'];

        // 检查库存是否足够
        if ($currentInventory < $count) {
            throw new \Exception('库存不足.预约失败');
        }

        // 扣减库存
        $newInventory = $currentInventory - $count;
        $result = \app\common\model\FilmSession::where('id', $filmSeeionId)
            ->update(['inventory_votes' => $newInventory]);

        if (!$result) {
            throw new \Exception('库存扣减失败');
        }
    }

    /**
     * 生成UUID
     * @return string
     */
    private function generateUuid()
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }
}