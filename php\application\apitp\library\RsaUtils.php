<?php

namespace app\apitp\library;

/**
 * RSA加密解密工具类
 * 对应Java中的RsaUtils类
 */
class RsaUtils
{
    /**
     * 公钥
     */
    public static $publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==';
    
    /**
     * 私钥
     */
    public static $privateKey = 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=';

    /**
     * 使用默认私钥解密
     * 对应Java中的decryptByPrivateKey(String text)方法
     * 
     * @param string $text 加密后的文本
     * @return string 解密后的文本
     * @throws \Exception
     */
    public static function decryptByPrivateKey($text)
    {
        return self::decryptByPrivateKeyWithKey(self::$privateKey, $text);
    }

    /**
     * 使用公钥解密
     * 对应Java中的decryptByPublicKey方法
     * 
     * @param string $publicKeyString 公钥字符串
     * @param string $text 加密后的文本
     * @return string 解密后的文本
     * @throws \Exception
     */
    public static function decryptByPublicKey($publicKeyString, $text)
    {
        try {
            // 解码公钥
            $publicKeyData = base64_decode($publicKeyString);
            $publicKey = openssl_pkey_get_public("-----BEGIN PUBLIC KEY-----\n" . chunk_split($publicKeyString, 64) . "-----END PUBLIC KEY-----");
            
            if (!$publicKey) {
                throw new \Exception('无效的公钥');
            }
            
            // 解密数据
            $encryptedData = base64_decode($text);
            $result = openssl_public_decrypt($encryptedData, $decrypted, $publicKey);
            
            if (!$result) {
                throw new \Exception('公钥解密失败');
            }
            
            return $decrypted;
        } catch (\Exception $e) {
            throw new \Exception('公钥解密失败: ' . $e->getMessage());
        }
    }

    /**
     * 使用私钥加密
     * 对应Java中的encryptByPrivateKey方法
     * 
     * @param string $privateKeyString 私钥字符串
     * @param string $text 要加密的文本
     * @return string 加密后的文本
     * @throws \Exception
     */
    public static function encryptByPrivateKey($privateKeyString, $text)
    {
        try {
            // 解码私钥
            $privateKey = openssl_pkey_get_private("-----BEGIN PRIVATE KEY-----\n" . chunk_split($privateKeyString, 64) . "-----END PRIVATE KEY-----");
            
            if (!$privateKey) {
                throw new \Exception('无效的私钥');
            }
            
            // 加密数据
            $result = openssl_private_encrypt($text, $encrypted, $privateKey);
            
            if (!$result) {
                throw new \Exception('私钥加密失败');
            }
            
            return base64_encode($encrypted);
        } catch (\Exception $e) {
            throw new \Exception('私钥加密失败: ' . $e->getMessage());
        }
    }

    /**
     * 使用指定私钥解密
     * 对应Java中的decryptByPrivateKey(String privateKeyString, String text)方法
     * 
     * @param string $privateKeyString 私钥字符串
     * @param string $text 加密后的文本
     * @return string 解密后的文本
     * @throws \Exception
     */
    public static function decryptByPrivateKeyWithKey($privateKeyString, $text)
    {
        try {
            // 解码私钥
            $privateKey = openssl_pkey_get_private("-----BEGIN PRIVATE KEY-----\n" . chunk_split($privateKeyString, 64) . "-----END PRIVATE KEY-----");
            
            if (!$privateKey) {
                throw new \Exception('无效的私钥');
            }
            
            // 解密数据
            $encryptedData = base64_decode($text);
            $result = openssl_private_decrypt($encryptedData, $decrypted, $privateKey);
            
            if (!$result) {
                throw new \Exception('私钥解密失败');
            }
            
            return $decrypted;
        } catch (\Exception $e) {
            throw new \Exception('私钥解密失败: ' . $e->getMessage());
        }
    }

    /**
     * 使用公钥加密
     * 对应Java中的encryptByPublicKey方法
     * 
     * @param string $publicKeyString 公钥字符串
     * @param string $text 要加密的文本
     * @return string 加密后的文本
     * @throws \Exception
     */
    public static function encryptByPublicKey($publicKeyString, $text)
    {
        try {
            // 解码公钥
            $publicKey = openssl_pkey_get_public("-----BEGIN PUBLIC KEY-----\n" . chunk_split($publicKeyString, 64) . "-----END PUBLIC KEY-----");
            
            if (!$publicKey) {
                throw new \Exception('无效的公钥');
            }
            
            // 加密数据
            $result = openssl_public_encrypt($text, $encrypted, $publicKey);
            
            if (!$result) {
                throw new \Exception('公钥加密失败');
            }
            
            return base64_encode($encrypted);
        } catch (\Exception $e) {
            throw new \Exception('公钥加密失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成RSA密钥对
     * 对应Java中的generateKeyPair方法
     * 
     * @return RsaKeyPair 密钥对对象
     * @throws \Exception
     */
    public static function generateKeyPair()
    {
        try {
            // 生成密钥对
            $config = [
                'digest_alg' => 'sha256',
                'private_key_bits' => 1024,
                'private_key_type' => OPENSSL_KEYTYPE_RSA,
            ];
            
            $resource = openssl_pkey_new($config);
            
            if (!$resource) {
                throw new \Exception('密钥对生成失败');
            }
            
            // 导出私钥
            openssl_pkey_export($resource, $privateKey);
            
            // 获取公钥
            $publicKeyDetails = openssl_pkey_get_details($resource);
            $publicKey = $publicKeyDetails['key'];
            
            // 提取密钥字符串（去掉头尾标识）
            $publicKeyString = str_replace(['-----BEGIN PUBLIC KEY-----', '-----END PUBLIC KEY-----', "\n", "\r"], '', $publicKey);
            $privateKeyString = str_replace(['-----BEGIN PRIVATE KEY-----', '-----END PRIVATE KEY-----', "\n", "\r"], '', $privateKey);
            
            return new RsaKeyPair($publicKeyString, $privateKeyString);
        } catch (\Exception $e) {
            throw new \Exception('密钥对生成失败: ' . $e->getMessage());
        }
    }
}

/**
 * RSA密钥对类
 * 对应Java中的RsaKeyPair内部类
 */
class RsaKeyPair
{
    private $publicKey;
    private $privateKey;

    public function __construct($publicKey, $privateKey)
    {
        $this->publicKey = $publicKey;
        $this->privateKey = $privateKey;
    }

    public function getPublicKey()
    {
        return $this->publicKey;
    }

    public function getPrivateKey()
    {
        return $this->privateKey;
    }
}