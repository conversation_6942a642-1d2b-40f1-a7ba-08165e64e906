<template>
	<view class="contactContent">
		<Header menuClass="bor" :isFixed="true" :isBack="true" :isShowHome="true" :title="'联系人'" :color="'#000'">
		</Header>
		<Header></Header>
		<view class="contactBox">
			<view class="addContact" @click="addContact">
				<text class="add">+</text>添加联系人
			</view>
			<view class="contactList">
				<view class="contactItem" v-for="(link,index) in linkList" :key="link.id" @touchstart='touchstart'
					@touchmove='touchmove' :data-index=index @click.stop="chooseLink(index)">
					<view class="left">
						<view class="peopleName">
							{{link.linkmanName}}
						</view>
						<view class="peopleCard">
							身份证 {{ hideCardNum(index) }}
						</view>
						<view class="peopleMablie">
							<text>手机号码 {{link.linkmanPhone}}</text>
							<text>年龄{{link.linkmanAge}}</text>
						</view>
					</view>
					<view class="right">
						<view :class="['checkBtn',link.isCheck?'isCheck':'']"></view>
					</view>
					<view :class="['handlePlace',item.isMove?'isMove':'']">
						<view class="edit" @click="editItem(index)">编辑</view>
						<view class="del" @click="deleteItem(index)">删除</view>
					</view>
				</view>
			</view>
		</view>
		<view class="sureChoose">
			<view class="showNum">
				选择 <text class="setNum">{{checkAllNum}}</text> 人
			</view>
			<view class="upBtn" @click="sureNum">
				确定选择
			</view>
		</view>
	</view>
</template>

<script>
	import {
		header
	} from '@/component/header.vue';

	export default {
		data() {
			return {
				maxNum: 0,
				linkList: [],
				checkAllNum: 0,
				startX: '',
				startY: '',
			};
		},
		computed: {
			hideCardNum: function() {
				return (index) => {
					let num = this.linkList[index].linkmanCertificate.replace(this.linkList[index]
						.linkmanCertificate.substring(4, 15),
						"*******");

					return num;
				};
			}
		},
		components: {
			header,
		},
		onShow() {
			this.getLinkList()
		},
		onLoad(option) {
			/**
			 * type类型有三个
			 * 
			 * 入馆预约(rgyy_link)
			 * 课程预约(kcyy_link)
			 * 观影预约(gyyy_link)
			 * 
			 * */
			this.maxNum = option.num;
			this.type = option.type || '';
		},
		methods: {
			getLinkList() {
				this.$myRequest({
					url: `/apitp/UserLinkman/list`,
				}).then((res) => {
					this.linkList = res.data.data.length && res.data.data.map((item) => {
						return {
							...item,
							isCheck: false
						}
					})
				})
			},
			addContact() {
				uni.navigateTo({
					url: '/pages/contacts/addcontact'
				})
			},
			chooseLink(index) {
				this.linkList[index].isCheck = !this.linkList[index].isCheck;

				this.checkAllNum = (this.linkList.filter((item) => {
					return item.isCheck === true;
				})).length;
			},
			sureNum() {
				if (this.checkAllNum > this.maxNum) {
					uni.showToast({
						title: '选择人数超出',
						icon: 'error',
						duration: 2000
					});
					return;
				} else if (this.checkAllNum < this.maxNum) {
					uni.showToast({
						title: '选择人数不足',
						icon: 'error',
						duration: 2000
					});
					return;
				}
				let checkList = this.linkList.filter((item) => {
					return item.isCheck === true;
				});

				uni.setStorageSync(this.type, JSON.stringify(checkList));
				uni.navigateBack();
			},
			deleteItem(index) {
				this.linkList.splice(index, 1);
			},
			editItem(index) {
				uni.navigateTo({
					url: `/pages/contacts/addcontact?id=${index}`
				})
			},
			touchstart(e) {
				this.linkList.map(ele => {
					if (ele.isMove) {
						ele.isMove = false;
					}
				})
				this.startX = e.changedTouches[0].clientX;
				this.startY = e.changedTouches[0].clientY;
			},
			touchmove(e) {
				let index = e.currentTarget.dataset.index,
					startX = this.startX,
					startY = this.startY,
					touchMoveX = e.changedTouches[0].clientX, //滑动变化坐标
					touchMoveY = e.changedTouches[0].clientY, //滑动变化坐标
					angle = this.angle({
						x: startX,
						y: startY
					}, {
						x: touchMoveX,
						y: touchMoveY
					});
				if (Math.abs(angle) > 30) return;
				if (startX - touchMoveX >= 30) {
					this.linkList[index].isMove = true;
				}
			},
			// 计算滑动角度
			angle(start, end) {
				let x = end.x - start.x,
					y = end.y - start.y;
				return 360 * Math.atan(y / x) / (Math.PI)
			}
		}
	}
</script>

<style lang="less" scoped>
	.contactContent {
		width: 100%;
		height: 100vh;
		font-family: 'PingFang SC';
		overflow: scroll;
		background-color: #F3F4F6;

		.contactBox {
			width: 100%;
			height: auto;
			box-sizing: border-box;
			padding: 29upx 28upx 29upx 28upx;
			margin-bottom: 200upx;

			.addContact {
				width: 100%;
				height: 87upx;
				line-height: 87upx;
				text-align: center;
				border-radius: 10upx;
				font-size: 35upx;
				color: #5CB7FF;
				font-weight: 600;
				background-color: #fff;
				margin-bottom: 28upx;
				
				.add{
					font-size: 42upx;
					margin-right: 5upx;
				}
			}

			.contactList {
				width: 100%;
				height: auto;
				overflow: hidden;

				.contactItem {
					width: 100%;
					height: 158upx;
					box-sizing: border-box;
					padding: 18upx 28upx;
					display: flex;
					justify-content: space-between;
					position: relative;
					background-color: #fff;
					border-radius: 10upx;
					margin-bottom: 20upx;


					.left {
						width: auto;
						height: 100%;
						display: flex;
						flex-direction: column;
						justify-content: center;

						.peopleName {
							color: #000;
							font-size: 29upx;
							font-weight: 600;
							margin-bottom: 10upx;
						}

						.peopleCard,
						.peopleMablie {
							color: #888;
							font-size: 23upx;
						}

						.peopleMablie {
							text {
								&:first-child {
									display: inline-block;
									margin-right: 96upx;
								}
							}
						}
					}

					.right {
						width: 38upx;
						height: 100%;
						display: flex;
						align-items: center;

						.checkBtn {
							width: 38upx;
							height: 38upx;
							line-height: 38upx;
							text-align: center;
							border: 2upx solid #888888;
							border-radius: 38upx;
							font-weight: 700;
							box-sizing: border-box;

							&.isCheck {
								background: #FFBA38;
								border: none;
								font-family: 'icongou';

								&::before {
									content: "\e66c";
									display: inline-block;
								}
							}
						}
					}

					.handlePlace {
						width: 0;
						height: 100%;
						display: flex;
						position: absolute;
						top: 0;
						right: 0;
						font-size: 28upx;
						color: #fff;
						text-align: center;
						line-height: 158upx;
						z-index: 99;
						transition: all 0.3s;

						.edit {
							width: 50%;
							height: 100%;
							background-color: #5CB7FF;
						}

						.del {
							width: 50%;
							height: 100%;
							background-color: #fc5531;
						}

						&.isMove {
							width: 40%;
						}
					}

				}
			}

		}

		.sureChoose {
			width: 100%;
			height: 150upx;
			background-color: #fff;
			box-shadow: 20upx 10upx 20upx 10upx rgba(0, 0, 0, 0.4);
			position: fixed;
			bottom: 0;
			left: 0;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-sizing: border-box;
			padding: 0 29upx;

			.showNum {
				font-size: 27upx;
				color: #888888;

				.setNum {
					font-size: 31upx;
					color: #FFBA38;
				}
			}

			.upBtn {
				width: 362upx;
				height: 77upx;
				text-align: center;
				line-height: 77upx;
				background: #5CB7FF;
				box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
				border-radius: 10upx;
				font-size: 35upx;
				color: #fff;
			}
		}
	}
</style>
