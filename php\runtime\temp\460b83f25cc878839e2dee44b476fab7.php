<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:78:"/mnt/f/kejiguan/php/public/../application/admin/view/redisadmin/dashboard.html";i:1753846497;s:62:"/mnt/f/kejiguan/php/application/admin/view/layout/default.html";i:1753348167;s:59:"/mnt/f/kejiguan/php/application/admin/view/common/meta.html";i:1753348167;s:61:"/mnt/f/kejiguan/php/application/admin/view/common/script.html";i:1753348167;}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
<title><?php echo (isset($title) && ($title !== '')?$title:''); ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="referrer" content="never">
<meta name="robots" content="noindex, nofollow">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<!-- Loading Bootstrap -->
<link href="/assets/css/backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">

<?php if(\think\Config::get('fastadmin.adminskin')): ?>
<link href="/assets/css/skins/<?php echo htmlentities(\think\Config::get('fastadmin.adminskin') ?? ''); ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">
<?php endif; ?>

<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config:  <?php echo json_encode($config ?? ''); ?>
    };
</script>

    </head>

    <body class="inside-header inside-aside <?php echo defined('IS_DIALOG') && IS_DIALOG ? 'is-dialog' : ''; ?>">
        <div id="main" role="main">
            <div class="tab-content tab-addtabs">
                <div id="content">
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            <section class="content-header hide">
                                <h1>
                                    <?php echo __('Dashboard'); ?>
                                    <small><?php echo __('Control panel'); ?></small>
                                </h1>
                            </section>
                            <?php if(!IS_DIALOG && !\think\Config::get('fastadmin.multiplenav') && \think\Config::get('fastadmin.breadcrumb')): ?>
                            <!-- RIBBON -->
                            <div id="ribbon">
                                <ol class="breadcrumb pull-left">
                                    <?php if($auth->check('dashboard')): ?>
                                    <li><a href="dashboard" class="addtabsit"><i class="fa fa-dashboard"></i> <?php echo __('Dashboard'); ?></a></li>
                                    <?php endif; ?>
                                </ol>
                                <ol class="breadcrumb pull-right">
                                    <?php foreach($breadcrumb as $vo): ?>
                                    <li><a href="javascript:;" data-url="<?php echo htmlentities($vo['url'] ?? ''); ?>"><?php echo htmlentities($vo['title'] ?? ''); ?></a></li>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                            <!-- END RIBBON -->
                            <?php endif; ?>
                            <div class="content">
                                <div class="panel panel-default">
    <div class="panel-heading">基本信息</div>
    <div class="panel-body">
        <div class="row">
            <?php if(is_array($base) || $base instanceof \think\Collection || $base instanceof \think\Paginator): if( count($base)==0 ) : echo "" ;else: foreach($base as $key=>$val): ?>
            <div class="col-md-3 col-sm-6 col-xs-12">
                <div class="box box-solid" style="border:1px solid #eee;padding:10px;margin-bottom:10px;">
                    <div style="font-size:13px;color:#666;"><?php echo $key; ?></div>
                    <div style="font-weight:bold;font-size:15px;"><?php echo $val; ?></div>
                </div>
            </div>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="panel panel-default">
            <div class="panel-heading">Key 类型统计</div>
            <div class="panel-body">
                <canvas id="chartType" style="height: 300px;"></canvas>

            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="panel panel-default">
            <div class="panel-heading">内存信息</div>
            <div class="panel-body">
                <div id="usedmemory" style="height: 300px;"></div>

            </div>
        </div>
    </div>
</div>




<!-- 引入 Chart.js 和 ECharts -->
<script src="/assets/js/chart.js"></script>
<!-- ECharts JS 引入 -->
<script src="/assets/js/echarts.min.js"></script>
<script src="/assets/js/echarts-theme.js"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/echarts@4.9.0/theme/macarons.js"></script> -->



<script>
    // Key 类型饼图
    const statData = <?php echo $stats_json; ?>;
      const ctx = document.getElementById('chartType').getContext('2d');
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: Object.keys(statData),
            datasets: [{
                data: Object.values(statData),
                borderWidth: 1
            }]
        },
        options: {
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });




    // 后端变量嵌入
    const usedMemory = <?php echo isset($used_memory)?$used_memory: 0; ?>;
    const maxMemory = <?php echo isset($max_memory)?$max_memory: 100 * 1024 * 1024; ?>; // 默认100MB
    const usedMemoryHuman = "<?php echo isset($used_memory_human)?$used_memory_human: '-'; ?>";
    const maxMemoryHuman = "<?php echo isset($max_memory_human)?$max_memory_human: '100MB'; ?>";

    // 初始化图表
    const memChart = echarts.init(document.getElementById('usedmemory'), 'macarons');

    // 设置图表选项
    memChart.setOption({
        tooltip: {
            formatter: `当前内存: ${usedMemoryHuman}<br/>上限: ${maxMemoryHuman}`
        },
        series: [
            {
                name: '内存使用',
                type: 'gauge',
                min: 0,
                max: maxMemory / (1024 * 1024), // 转为MB
                detail: {
                    formatter: usedMemoryHuman,
                },
                data: [
                    {
                        value: (usedMemory / (1024 * 1024)).toFixed(2), // 转MB，保留两位小数
                        name: '内存消耗',
                    }
                ]
            }
        ]
    });

</script>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="/assets/js/require.min.js" data-main="/assets/js/require-backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.js?v=<?php echo htmlentities($site['version'] ?? ''); ?>"></script>

    </body>
</html>
