<?php

namespace app\common\library;

/**
 * DES加密类 - 与Java端加密算法保持一致
 * 基于Java端com.iwesalt.common.utils.des类的实现
 */
class DesEncryption
{
    // Java端使用的密钥
    private static $COMM_KEY = [
        105, 106, 109, 101, 100, 111, 107, 109, 
        102, 97, 111, 104, 102, 97, 111, 105
    ];
    
    /**
     * 加密数据
     * @param string $data 要加密的数据
     * @return string 加密后的16进制字符串
     */
    public static function encrypt($data)
    {
        $dataBytes = self::stringToBytes($data);
        $encrypted = self::encryptDecryptBuf($dataBytes);
        return self::bytesToHexString($encrypted);
    }
    
    /**
     * 解密数据
     * @param string $hexData 16进制格式的加密数据
     * @return string 解密后的原始数据
     */
    public static function decrypt($hexData)
    {
        $dataBytes = self::hexStringToBytes($hexData);
        $decrypted = self::encryptDecryptBuf($dataBytes);
        return self::bytesToString($decrypted);
    }
    
    /**
     * 与Java端encryptDecryptBuf方法对应的实现
     * @param array $pBuf 字节数组
     * @return array 处理后的字节数组
     */
    private static function encryptDecryptBuf($pBuf)
    {
        $j = 0;
        $result = [];
        
        for ($i = 0; $i < count($pBuf); $i++) {
            $result[$i] = ($pBuf[$i] ^ self::$COMM_KEY[$j]) & 0xFF;
            if (++$j == count(self::$COMM_KEY)) {
                $j = 0;
            }
        }
        
        return $result;
    }
    
    /**
     * 字符串转字节数组
     * @param string $str
     * @return array
     */
    private static function stringToBytes($str)
    {
        $bytes = [];
        for ($i = 0; $i < strlen($str); $i++) {
            $bytes[] = ord($str[$i]);
        }
        return $bytes;
    }
    
    /**
     * 字节数组转字符串
     * @param array $bytes
     * @return string
     */
    private static function bytesToString($bytes)
    {
        $str = '';
        foreach ($bytes as $byte) {
            $str .= chr($byte);
        }
        return $str;
    }
    
    /**
     * 16进制字符串转字节数组
     * @param string $hex
     * @return array
     */
    private static function hexStringToBytes($hex)
    {
        $hex = str_replace(' ', '', $hex);
        $bytes = [];
        
        for ($i = 0; $i < strlen($hex); $i += 2) {
            $bytes[] = hexdec(substr($hex, $i, 2));
        }
        
        return $bytes;
    }
    
    /**
     * 字节数组转16进制字符串
     * @param array $bytes
     * @return string
     */
    private static function bytesToHexString($bytes)
    {
        $hex = '';
        foreach ($bytes as $byte) {
            $hex .= sprintf('%02x', $byte);
        }
        return $hex;
    }
    
    /**
     * 生成二维码加密数据
     * @param array $qrData 二维码原始数据
     * @return string 加密后的数据
     */
    public static function generateQrCode($qrData)
    {
        $jsonData = json_encode($qrData, JSON_UNESCAPED_UNICODE);
        $encrypted = self::encrypt($jsonData);
        return strtoupper($encrypted);
    }
    
    /**
     * 解析二维码加密数据
     * @param string $encryptedData 加密数据
     * @return array 解密后的数据
     */
    public static function parseQrCode($encryptedData)
    {
        try {
            $decrypted = self::decrypt($encryptedData);
            $data = json_decode($decrypted, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON data');
            }
            
            return $data;
        } catch (\Exception $e) {
            throw new \Exception('二维码解析失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 验证二维码数据格式
     * @param array $data
     * @return bool
     */
    public static function validateQrData($data)
    {
        $requiredFields = ['id', 'filmSeeionId', 'userId', 'name', 'sfid', 'showTime', 'time', 'code', 'codeType'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                return false;
            }
        }
        
        return true;
    }
}