<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\CourseSubscribe;
use app\common\model\FilmSubscribe;
use app\common\model\VenueSubscribe;
use think\Db;
use think\Validate;
use think\Cache;


class UserLinkman extends Api
{
    protected $model = null;
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\UserLinkman;
    }

    /**
     * 获取联系人列表（分页）
     * GET: /admin/userlinkman/list
     */
    public function list()
    {
        $user = $this->auth->getUser();

        // 构建查询条件
        $where = [
            'user_id' => ['=', $user->user_id],
            'del_flag' => ['=', '0'],
        ];

        // 查询数据
        $list = $this->model
            ->field('id, user_id, linkman_name, linkman_phone, linkman_age, linkman_certificate, linkman_certificate_type')
            ->where($where)
            ->order('id desc')
            ->paginate();

        $rows = $list->items();
        foreach ($rows as &$item) {
            $item = [

                'userId' => $item['user_id'],
                'linkmanName' => !empty($item['linkman_name']) ? desensitize_name($item['linkman_name']) : '',
                'linkmanPhone' => !empty($item['linkman_phone']) ? desensitize_phone($item['linkman_phone']) : '',
                'linkmanAge' => $item['linkman_age'],
                'linkmanCertificate' => !empty($item['linkman_certificate']) ? desensitize_certificate($item['linkman_certificate']) : '',
                'linkmanCertificateType' => $item['linkman_certificate_type'],
            ];
        }

        return $this->success('获取成功', $rows ?? []);
    }


     /**
     * 添加联系人
     * 
     * @ApiTitle    (添加联系人)
     * @ApiSummary  (添加新的联系人信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/apitp/userlinkman/add)
     */
    public function add()
    {
     
            $user = $this->auth->getUser();
            $userId = $user->user_id;
            $data = $this->request->post();  
            // 验证必填字段
            $validate = new Validate([
                'linkman_name' => 'require|max:50',
                'linkman_phone' => 'require|mobile',
                'linkman_certificate' => 'require|max:50'
            ], [
                'linkman_name.require' => '联系人姓名不能为空',
                'linkman_name.max' => '联系人姓名不能超过50个字符',
                'linkman_phone.require' => '手机号不能为空',
                'linkman_phone.mobile' => '请输入正确的手机号码',
                'linkman_certificate.require' => '证件号码不能为空',
                'linkman_certificate.max' => '证件号码不能超过50个字符'
            ]);

            if (!$validate->check($data)) {
                $this->error($validate->getError());
            }

            // 验证身份证号码格式（如果是身份证）
            if (isset($data['linkman_certificate_type']) && $data['linkman_certificate_type'] == 0) {
                if (!$this->validateIdCard($data['linkman_certificate'])) {
                    $this->error('证件号码不合规');
                }
            }

            // 检查手机号是否已存在
            $exists =  $this->model
                ->where('user_id', $userId)
                ->where('linkman_phone', $data['linkman_phone'])
                ->where('del_flag', '0')
                ->find();
            
            if ($exists) {
                $this->error('该手机号已存在');
            }

            // 添加用户ID和时间戳
            $data['user_id'] = $userId;
            $data['create_time'] = date('Y-m-d H:i:s');
            $data['update_time'] = date('Y-m-d H:i:s');
            $data['del_flag'] = '0';

            $result =  $this->model->insert($data);
            
            if ($result) {
                $this->success('添加成功');
            } else {
                $this->error('添加失败');
            }
    
    }





        /**
     * 验证身份证号码
     */
    private function validateIdCard($idCard)
    {
        // 简单的身份证号码验证
        if (strlen($idCard) != 18) {
            return false;
        }
        return preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $idCard);
    }





 /**
     * 编辑联系人
     * 
     */
    public function edit()
    {
    
        $userId = $this->auth->getUser()->user_id;
        if (!$userId) {
            $this->error('用户未登录', null, 401);
        }

        $data = $this->request->post();
        $id = $data['id'] ?? null;
        
        if (!$id) {
            $this->error('联系人ID不能为空');
        }

        // 验证联系人是否存在且属于当前用户
        $linkman = $this->model
            ->where('id', $id)
            ->where('user_id', $userId)
            ->where('del_flag', '0')
            ->find();

        if (!$linkman) {
            $this->error('联系人不存在或无权限访问');
        }

        // 验证必填字段
        $validate = new Validate([
            'linkman_name' => 'require|max:50',
            'linkman_certificate' => 'require|max:50'
        ], [
            'linkman_name.require' => '联系人姓名不能为空',
            'linkman_name.max' => '联系人姓名不能超过50个字符',
            'linkman_certificate.require' => '证件号码不能为空',
            'linkman_certificate.max' => '证件号码不能超过50个字符'
        ]);

        if (!$validate->check($data)) {
            $this->error($validate->getError());
        }

        // 验证身份证号码格式（如果是身份证）
        if (isset($data['linkman_certificate_type']) && $data['linkman_certificate_type'] == 0) {
            if (!$this->validateIdCard($data['linkman_certificate'])) {
                $this->error('证件号码不合规');
            }
        }

        // 更新数据
        unset($data['id']);
        $data['update_time'] = date('Y-m-d H:i:s');

        $result =  $this->model
            ->where('id', $id)
            ->where('user_id', $userId)
            ->update($data);
        
        if ($result !== false) {
            $this->success('更新成功');
        } else {
            $this->error('更新失败');
        }
       
    }

    /**
     * 删除联系人
     * 
     * @ApiTitle    (删除联系人)
     * @ApiSummary  (删除联系人信息)
     * @ApiMethod   (DELETE)
     * @ApiRoute    (/auth/linkman/{ids})
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="ids", type="string", required=true, description="联系人ID，多个用逗号分隔")
     */
    public function delete()
    {
        try {
             $user = $this->auth->getUser();
            $userId = $user->user_id;
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }

            $ids = $this->request->param('ids');
            if (!$ids) {
                $this->error('联系人ID不能为空');
            }

            $idArray = explode(',', $ids);
            
            // 验证所有联系人都属于当前用户
            foreach ($idArray as $id) {
                $linkman =  $this->model
                    ->where('id', $id)
                    ->where('user_id', $userId)
                    ->where('del_flag', '0')
                    ->find();
                
                if (!$linkman) {
                    $this->error('联系人不存在或无权限访问');
                }
            }

            // 软删除
            $result =  $this->model
                ->where('id', 'in', $idArray)
                ->where('user_id', $userId)
                ->update([
                    'del_flag' => '2',
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            
            if ($result) {
                $this->success('删除成功');
            } else {
                $this->error('删除失败');
            }
        } catch (\Exception $e) {
            $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查签到状态
     * 
     * @ApiTitle    (检查签到状态)
     * @ApiSummary  (获取用户中心消息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/auth/linkman/isSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function isSign()
    {
        try {
            $userId = $this->auth->getUser()->user_id;
            // 获取用户中心消息，模拟Java版本的getUserCenterMsg方法
            $userMsgVo = $this->getUserCenterMsg($userId);
            
            $this->success('获取成功', $userMsgVo);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户中心消息
     * 对应Java版本的getUserCenterMsg方法
     * 
     * @param int $userId 用户ID
     * @return array
     */
    private function getUserCenterMsg($userId)
    {
        $currentDate = date('Y-m-d H:i:s');
        
        // 查询电影相关数据
        $filmNum = FilmSubscribe::alias('fs')
            ->join('film_session fse', 'fs.film_session_id = fse.id')
            ->where('fs.user_id', $userId)
            ->where('fs.del_flag', '0')
            ->where('fse.film_start_time', '>=', $currentDate)
            ->count();
            
        // 查询课程相关数据
        $courseNum = CourseSubscribe::alias('cs')
            ->join('course_session cse', 'cs.course_session_id = cse.id')
            ->where('cs.user_id', $userId)
            ->where('cs.del_flag', '0')
            ->where('cse.course_start_time', '>=', $currentDate)
            ->count();
            
        // 查询场馆相关数据
        $venueNum = VenueSubscribe::alias('vs')
            ->join('venue v', 'vs.venue_id = v.id')
            ->where('vs.user_id', $userId)
            ->where('vs.del_flag', '0')
            ->where('v.venue_start_time', '>=', $currentDate)
            ->count();
        
        // 构建返回数据，保持与Java版本UserMsgVo结构一致
        return [
            'film' => [
                'num' => (int)$filmNum
            ],
            'course' => [
                'num' => (int)$courseNum
            ],
            'venue' => [
                'num' => (int)$venueNum
            ]
        ];
    }

}

