<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:73:"/mnt/f/kejiguan/php/public/../application/admin/view/redisadmin/view.html";i:1753348167;s:62:"/mnt/f/kejiguan/php/application/admin/view/layout/default.html";i:1753348167;s:59:"/mnt/f/kejiguan/php/application/admin/view/common/meta.html";i:1753348167;s:61:"/mnt/f/kejiguan/php/application/admin/view/common/script.html";i:1753348167;}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
<title><?php echo (isset($title) && ($title !== '')?$title:''); ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="referrer" content="never">
<meta name="robots" content="noindex, nofollow">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<!-- Loading Bootstrap -->
<link href="/assets/css/backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">

<?php if(\think\Config::get('fastadmin.adminskin')): ?>
<link href="/assets/css/skins/<?php echo htmlentities(\think\Config::get('fastadmin.adminskin') ?? ''); ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">
<?php endif; ?>

<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config:  <?php echo json_encode($config ?? ''); ?>
    };
</script>

    </head>

    <body class="inside-header inside-aside <?php echo defined('IS_DIALOG') && IS_DIALOG ? 'is-dialog' : ''; ?>">
        <div id="main" role="main">
            <div class="tab-content tab-addtabs">
                <div id="content">
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            <section class="content-header hide">
                                <h1>
                                    <?php echo __('Dashboard'); ?>
                                    <small><?php echo __('Control panel'); ?></small>
                                </h1>
                            </section>
                            <?php if(!IS_DIALOG && !\think\Config::get('fastadmin.multiplenav') && \think\Config::get('fastadmin.breadcrumb')): ?>
                            <!-- RIBBON -->
                            <div id="ribbon">
                                <ol class="breadcrumb pull-left">
                                    <?php if($auth->check('dashboard')): ?>
                                    <li><a href="dashboard" class="addtabsit"><i class="fa fa-dashboard"></i> <?php echo __('Dashboard'); ?></a></li>
                                    <?php endif; ?>
                                </ol>
                                <ol class="breadcrumb pull-right">
                                    <?php foreach($breadcrumb as $vo): ?>
                                    <li><a href="javascript:;" data-url="<?php echo htmlentities($vo['url'] ?? ''); ?>"><?php echo htmlentities($vo['title'] ?? ''); ?></a></li>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                            <!-- END RIBBON -->
                            <?php endif; ?>
                            <div class="content">
                                <div class="panel panel-default">
    <div class="panel-heading">
        <strong>键值详情查看</strong>
    </div>
    <div class="panel-body">
        <div class="row" style="margin-bottom: 10px;">
            <div class="col-md-6"><strong>Key：</strong> <span class="text-primary"><?php echo $key; ?></span></div>
            <div class="col-md-3"><strong>类型：</strong> <span class="label label-info"><?php echo $type; ?></span></div>
            <div class="col-md-3"><strong>TTL：</strong> <span><?php echo $ttl; ?> 秒</span></div>
        </div>

        <div class="form-group">
            <label><strong>内容：</strong></label>
            <pre class="form-control" style="min-height:200px; white-space:pre-wrap; word-wrap:break-word;">

<?php if(is_array($value)): if(is_array($value) || $value instanceof \think\Collection || $value instanceof \think\Paginator): if( count($value)==0 ) : echo "" ;else: foreach($value as $k=>$v): ?>
{k} : <?php echo $v; endforeach; endif; else: echo "" ;endif; else: ?>
<?php echo $value; endif; ?>
            </pre>
        </div>

        <a href="<?php echo url('redisadmin/manage'); ?>" class="btn btn-default"><i class="fa fa-arrow-left"></i> 返回</a>
    </div>
</div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="/assets/js/require.min.js" data-main="/assets/js/require-backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.js?v=<?php echo htmlentities($site['version'] ?? ''); ?>"></script>

    </body>
</html>
