<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use think\Db;
use think\Validate;
use think\Cache;

/**
 * 联系人管理接口
 */
class Linkman extends Api
{
    // 所有接口都需要登录
    protected $noNeedLogin = [];
    // 所有接口都需要验证权限
    protected $noNeedRight = [];

    /**
     * 获取联系人列表
     * 
     * @ApiTitle    (获取联系人列表)
     * @ApiSummary  (获取当前用户的联系人列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/auth/linkman/list)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="200")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="获取成功")
     * @ApiReturnParams   (name="data", type="object", description="联系人列表数据")
     */
    public function list()
    {
        try {
            // 获取当前用户ID
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }

            // 分页参数
            $page = $this->request->get('page', 1);
            $limit = $this->request->get('limit', 10);
            
            // 查询联系人列表
            $list = Db::name('user_linkman')
                ->where('user_id', $userId)
                ->where('del_flag', '0')
                ->order('create_time desc')
                ->paginate($limit, false, ['page' => $page]);

            $result = [
                'total' => $list->total(),
                'rows' => $list->items()
            ];

            $this->success('获取成功', $result);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取联系人详情
     * 
     * @ApiTitle    (获取联系人详情)
     * @ApiSummary  (根据ID获取联系人详细信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/auth/linkman/{id})
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="id", type="integer", required=true, description="联系人ID")
     */
    public function info()
    {
        try {
            $id = $this->request->param('id');
            $userId = $this->getUserId();
            
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }

            if (!$id) {
                $this->error('联系人ID不能为空');
            }

            // 验证联系人是否存在且属于当前用户
            $linkman = Db::name('user_linkman')
                ->where('id', $id)
                ->where('user_id', $userId)
                ->where('del_flag', '0')
                ->find();

            if (!$linkman) {
                $this->error('联系人不存在或无权限访问');
            }

            $this->success('获取成功', $linkman);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加联系人
     * 
     * @ApiTitle    (添加联系人)
     * @ApiSummary  (添加新的联系人信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/auth/linkman/add)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="linkman_name", type="string", required=true, description="联系人姓名")
     * @ApiParams   (name="linkman_phone", type="string", required=true, description="联系人手机号")
     * @ApiParams   (name="linkman_certificate", type="string", required=true, description="联系人证件号")
     * @ApiParams   (name="linkman_certificate_type", type="integer", required=false, description="证件类型")
     * @ApiParams   (name="linkman_age", type="string", required=false, description="联系人年龄")
     */
    public function add()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }

            $data = $this->request->post();
            
            // 验证必填字段
            $validate = new Validate([
                'linkman_name' => 'require|max:50',
                'linkman_phone' => 'require|mobile',
                'linkman_certificate' => 'require|max:50'
            ], [
                'linkman_name.require' => '联系人姓名不能为空',
                'linkman_name.max' => '联系人姓名不能超过50个字符',
                'linkman_phone.require' => '手机号不能为空',
                'linkman_phone.mobile' => '请输入正确的手机号码',
                'linkman_certificate.require' => '证件号码不能为空',
                'linkman_certificate.max' => '证件号码不能超过50个字符'
            ]);

            if (!$validate->check($data)) {
                $this->error($validate->getError());
            }

            // 验证身份证号码格式（如果是身份证）
            if (isset($data['linkman_certificate_type']) && $data['linkman_certificate_type'] == 0) {
                if (!$this->validateIdCard($data['linkman_certificate'])) {
                    $this->error('证件号码不合规');
                }
            }

            // 检查手机号是否已存在
            $exists = Db::name('user_linkman')
                ->where('user_id', $userId)
                ->where('linkman_phone', $data['linkman_phone'])
                ->where('del_flag', '0')
                ->find();
            
            if ($exists) {
                $this->error('该手机号已存在');
            }

            // 添加用户ID和时间戳
            $data['user_id'] = $userId;
            $data['create_time'] = date('Y-m-d H:i:s');
            $data['update_time'] = date('Y-m-d H:i:s');
            $data['del_flag'] = '0';

            $result = Db::name('user_linkman')->insert($data);
            
            if ($result) {
                $this->success('添加成功');
            } else {
                $this->error('添加失败');
            }
        } catch (\Exception $e) {
            $this->error('添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 编辑联系人
     * 
     * @ApiTitle    (编辑联系人)
     * @ApiSummary  (编辑联系人信息)
     * @ApiMethod   (PUT)
     * @ApiRoute    (/auth/linkman/edit)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="id", type="integer", required=true, description="联系人ID")
     * @ApiParams   (name="linkman_name", type="string", required=true, description="联系人姓名")
     * @ApiParams   (name="linkman_phone", type="string", required=true, description="联系人手机号")
     * @ApiParams   (name="linkman_certificate", type="string", required=true, description="联系人证件号")
     */
    public function edit()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }

            $data = $this->request->put();
            $id = $data['id'] ?? null;
            
            if (!$id) {
                $this->error('联系人ID不能为空');
            }

            // 验证联系人是否存在且属于当前用户
            $linkman = Db::name('user_linkman')
                ->where('id', $id)
                ->where('user_id', $userId)
                ->where('del_flag', '0')
                ->find();

            if (!$linkman) {
                $this->error('联系人不存在或无权限访问');
            }

            // 验证必填字段
            $validate = new Validate([
                'linkman_name' => 'require|max:50',
                'linkman_certificate' => 'require|max:50'
            ], [
                'linkman_name.require' => '联系人姓名不能为空',
                'linkman_name.max' => '联系人姓名不能超过50个字符',
                'linkman_certificate.require' => '证件号码不能为空',
                'linkman_certificate.max' => '证件号码不能超过50个字符'
            ]);

            if (!$validate->check($data)) {
                $this->error($validate->getError());
            }

            // 验证身份证号码格式（如果是身份证）
            if (isset($data['linkman_certificate_type']) && $data['linkman_certificate_type'] == 0) {
                if (!$this->validateIdCard($data['linkman_certificate'])) {
                    $this->error('证件号码不合规');
                }
            }

            // 更新数据
            unset($data['id']);
            $data['update_time'] = date('Y-m-d H:i:s');

            $result = Db::name('user_linkman')
                ->where('id', $id)
                ->where('user_id', $userId)
                ->update($data);
            
            if ($result !== false) {
                $this->success('更新成功');
            } else {
                $this->error('更新失败');
            }
        } catch (\Exception $e) {
            $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除联系人
     * 
     * @ApiTitle    (删除联系人)
     * @ApiSummary  (删除联系人信息)
     * @ApiMethod   (DELETE)
     * @ApiRoute    (/auth/linkman/{ids})
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="ids", type="string", required=true, description="联系人ID，多个用逗号分隔")
     */
    public function delete()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }

            $ids = $this->request->param('ids');
            if (!$ids) {
                $this->error('联系人ID不能为空');
            }

            $idArray = explode(',', $ids);
            
            // 验证所有联系人都属于当前用户
            foreach ($idArray as $id) {
                $linkman = Db::name('user_linkman')
                    ->where('id', $id)
                    ->where('user_id', $userId)
                    ->where('del_flag', '0')
                    ->find();
                
                if (!$linkman) {
                    $this->error('联系人不存在或无权限访问');
                }
            }

            // 软删除
            $result = Db::name('user_linkman')
                ->where('id', 'in', $idArray)
                ->where('user_id', $userId)
                ->update([
                    'del_flag' => '2',
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            
            if ($result) {
                $this->success('删除成功');
            } else {
                $this->error('删除失败');
            }
        } catch (\Exception $e) {
            $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查签到状态
     * 
     * @ApiTitle    (检查签到状态)
     * @ApiSummary  (获取用户中心消息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/auth/linkman/isSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function isSign()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }

            // 这里可以根据业务需求实现具体的签到状态检查逻辑
            $userMsg = [
                'hasSign' => false,
                'signCount' => 0,
                'message' => '暂无签到信息'
            ];

            $this->success('获取成功', $userMsg);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId()
    {
        $token = $this->request->header('token') ?: $this->request->param('token');
        if (!$token) {
            return null;
        }

        $userData = Cache::store('redis')->get('wx:token:' . $token);
        if (!$userData || !isset($userData['user_id'])) {
            return null;
        }

        return $userData['user_id'];
    }

    /**
     * 验证身份证号码
     */
    private function validateIdCard($idCard)
    {
        // 简单的身份证号码验证
        if (strlen($idCard) != 18) {
            return false;
        }
        
        // 可以添加更复杂的身份证验证逻辑
        return preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $idCard);
    }
}