const Utils = {
	changeTime: function(time, isYear) {
		let times = new Date(time.replace(/-/g, '/'));
		let y = times.getFullYear();
		let m = (times.getMonth() + 1) < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
		let d = times.getDate() < 10 ? "0" + times.getDate() : times.getDate();

		let h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();
		let mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();
		let s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();
		if (isYear) {
			return y + '-' + m + '-' + d;
		} else {
			return h + ':' + mm
		}
	},
	changeTime2: function(time) {
		let times = new Date(time.replace(/-/g, '/'));
		let y = times.getFullYear();
		let m = (times.getMonth() + 1) < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
		let d = times.getDate() < 10 ? "0" + times.getDate() : times.getDate();

		let h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();
		let mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();
		let s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();
		return y + '-' + m + '-' + d +'  '+ h + ':' + mm + ':' + s;
	},	
	formatTime: function(time) {
		let times = new Date(time.replace(/-/g, '/'));
		let y = times.getFullYear();
		let m = (times.getMonth() + 1) < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
		let d = times.getDate() < 10 ? "0" + times.getDate() : times.getDate();

		let h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();
		let mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();
		let s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();
		return h + ':' + mm + ':' + s;
	},	
	checkMobile(str) {
		var reg = /^1[34578]\d{9}$/
		if (reg.test(str)) {
			return true;
		} else {
			return false;
		}
	},
	distance: function(la1, lo1, la2, lo2) {
		var La1 = la1 * Math.PI / 180.0;
		var La2 = la2 * Math.PI / 180.0;
		var La3 = La1 - La2;
		var Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;
		var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math
			.pow(Math.sin(Lb3 / 2), 2)));
		s = s * 6378.137;
		s = Math.round(s * 10000) / 10000;
		return s;
	},
	verifyIdCard: function(str, isLand = true) {
		/**
		 * isLand �Ƿ�Ϊ��½����֤
		 */
		var Land = /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/; //��½
		var Hongkong = /([A-Za-z](\d{6})\d)|(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/; //��ۡ���½
		var Taiwan = /^[a-zA-Z][0-9]{9}$/; //̨��
		var Macao = /^[1|5|7][0-9]{6}[09Aa]$/; //����

		if (isLand) {
			return Boolean(Land.test(str));
		} else {
			return Boolean(Hongkong.test(str) || Taiwan.test(str) || Macao.test(str));
		}

	},
	timeThenNow: function(times) {
		//�жϴ����ʱ���Ƿ񳬹���ǰ��ʱ��
		let now = new Date();
		return (new Date(times.replace(/-/g, '/')) > now);
	},
	// ����
	throttle: function(fn, interval) {
		var enterTime = 0; //������ʱ��
		var gapTime = interval || 3000; //���ʱ�䣬���interval��������Ĭ��300ms
		return function() {
			var context = this;
			var backTime = new Date(); //��һ�κ���return��������ʱ��
			if (backTime - enterTime > gapTime) {
				fn.call(context, arguments);
				enterTime = backTime; //��ֵ����һ�δ�����ʱ�䣬�����ͱ����˵ڶ��δ�����ʱ��
			}
		};
	},
	// ����
	debounce: function(fn, time) {
		//��ʼ��һ����һ�ε�ʱ��
		var timer
		var lastTime = 0;
		return function(path) {
			//ÿ�δ����¼�����������һ�εĶ�ʱ�����
			clearTimeout(timer);
			//��ȡ��ǰ��ʱ��
			var nowTime = Date.now();
			//�����thisָ�ľ���oBox��������¼���������������thisָ��oBox
			//����ڶ�ʱ����ֱ��ʹ��this����ôthisָ����Ƕ�ʱ��
			var _this = this;
			//arguments[0]���ǵ�ǰ�¼�������event����
			//����ڶ�ʱ����ֱ��ʹ��arguments[0]����ôarguments[0]ָ���Ƕ�ʱ��������ʵ��
			var e = arguments[0];
			timer = setTimeout(function() {
				if (nowTime - lastTime < time) {
					return;
				}
				//ʹ��call��������fn���������Ҹ���fn��thisָ��oBox
				fn.call(_this, e, path);
			}, time)
		}
	}
}


export default Utils;
