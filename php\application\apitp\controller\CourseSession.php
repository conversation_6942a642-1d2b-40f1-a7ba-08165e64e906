<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\CourseSession as CourseSessionModel;
use app\common\model\CourseSubscribe;
use app\common\model\WebUser;
use think\Db;
use think\Cache;
use app\apitp\library\RsaUtils;

/**
 * 课程场次管理接口
 */
class CourseSession extends Api
{
    // 所有接口都需要登录
    protected $noNeedLogin = [];
    // 所有接口都需要验证权限
    protected $noNeedRight = [];

    /**
     * 获取课程场次详情列表
     * 
     * @ApiTitle    (获取课程场次详情列表)
     * @ApiSummary  (根据查询条件获取课程场次详细信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/session/getSessionDetail)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="date", type="string", required=false, description="查询日期")
     * @ApiParams   (name="courseType", type="string", required=false, description="课程类型")
     * @ApiParams   (name="status", type="string", required=false, description="状态")
     */
    public function getSessionDetail()
    {
        try {
            $data = $this->request->post();
            
            // 构建查询条件
            $where = [];
            $where['del_flag'] = '0';
            
            if (isset($data['date']) && !empty($data['date'])) {
                $where['session_date'] = $data['date'];
            }
            
            if (isset($data['courseType']) && !empty($data['courseType'])) {
                $where['course_type'] = $data['courseType'];
            }
            
            if (isset($data['status']) && !empty($data['status'])) {
                $where['status'] = $data['status'];
            }
            
            // 查询课程场次列表
            $list = (new CourseSessionModel())->alias('cs')
                ->join('course c', 'cs.course_id = c.id', 'LEFT')
                ->field([
                    'cs.id',
                    'cs.course_id',
                    'cs.session_date',
                    'cs.session_time_start',
                    'cs.session_time_end',
                    'cs.max_people',
                    'cs.signed_people',
                    'cs.status',
                    'c.course_name',
                    'c.course_desc',
                    'c.course_type',
                    'c.age_limit'
                ])
                ->where($where)
                ->order('cs.session_date desc, cs.session_time_start asc')
                ->select();
            
            $this->success('获取成功', $list);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 根据ID获取课程详情
     * 
     * @ApiTitle    (获取课程详情)
     * @ApiSummary  (根据ID获取课程详细信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/getDetail/{id})
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="id", type="integer", required=true, description="课程ID")
     */
    public function getDetail()
    {
        try {
            $id = $this->request->param('id');
            
            if (!$id) {
                $this->error('课程ID不能为空');
            }
            
            // 查询课程详情
            $course = (new CourseSessionModel())->alias('cs')
                ->join('course c', 'cs.course_id = c.id', 'LEFT')
                ->field([
                    'cs.id',
                    'cs.course_id',
                    'cs.session_date',
                    'cs.session_time_start',
                    'cs.session_time_end',
                    'cs.max_people',
                    'cs.signed_people',
                    'cs.status',
                    'cs.venue',
                    'c.course_name',
                    'c.course_desc',
                    'c.course_type',
                    'c.age_limit',
                    'c.course_content',
                    'c.teacher_info'
                ])
                ->where('cs.id', $id)
                ->where('cs.del_flag', '0')
                ->find();
            
            if (!$course) {
                $this->error('课程不存在');
            }
            
            $this->success('获取成功', $course);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户信息（主要是手机号）
     * 
     * @ApiTitle    (获取用户信息)
     * @ApiSummary  (获取当前用户信息，手机号经过加密处理)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/getPhone)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function getPhone()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }
            
            // 查询用户信息
            $userInfo = WebUser::field(['user_id', 'user_name', 'nick_name', 'phonenumber', 'avatar'])
                ->where('user_id', $userId)
                ->where('del_flag', '0')
                ->find();
            
            if ($userInfo) {
                // 对手机号进行RSA加密处理
                if (!empty($userInfo['phonenumber'])) {
                    $userInfo['phone'] = $this->rsaEncrypt($userInfo['phonenumber']);
                    unset($userInfo['phonenumber']);
                } else {
                    $userInfo['phone'] = '';
                }
            } else {
                $userInfo = [
                    'user_id' => $userId,
                    'user_name' => '',
                    'nick_name' => '',
                    'phone' => '',
                    'avatar' => ''
                ];
            }
            
            $this->success('获取成功', $userInfo);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存用户手机号
     * 
     * @ApiTitle    (保存用户手机号)
     * @ApiSummary  (保存用户手机号信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/session/saveUserPhone)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="phone", type="string", required=true, description="加密后的手机号")
     */
    public function saveUserPhone()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }
            
            $data = $this->request->post();
            
            if (!isset($data['phone']) || empty($data['phone'])) {
                $this->error('手机号不能为空');
            }
            
            // 解密手机号（使用RSA私钥解密）
            $phone = $this->rsaDecrypt($data['phone']);
            
            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
                $this->error('手机号格式不正确');
            }
            
            // 更新用户手机号
            $result = WebUser::where('user_id', $userId)
                ->update([
                    'phonenumber' => $phone,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            
            $this->success('保存成功', $result);
        } catch (\Exception $e) {
            $this->error('保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 课程报名/签到
     * 
     * @ApiTitle    (课程报名)
     * @ApiSummary  (用户报名参加课程)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/session/signCourse)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="courseId", type="integer", required=true, description="课程ID")
     * @ApiParams   (name="peopleIds", type="array", required=true, description="参与人员ID列表")
     * @ApiParams   (name="emergencyContact", type="string", required=false, description="紧急联系人")
     * @ApiParams   (name="emergencyPhone", type="string", required=false, description="紧急联系电话")
     */
    public function signCourse()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }
            
            $data = $this->request->post();
            
            // 验证必填参数
            if (!isset($data['courseId']) || empty($data['courseId'])) {
                $this->error('课程ID不能为空');
            }
            
            if (!isset($data['peopleIds']) || empty($data['peopleIds'])) {
                $this->error('参与人员不能为空');
            }
            
            $courseId = $data['courseId'];
            $peopleIds = is_array($data['peopleIds']) ? $data['peopleIds'] : explode(',', $data['peopleIds']);
            
            // 检查课程是否存在且可报名
            $course = (new CourseSessionModel())->where('id', $courseId)
                ->where('del_flag', '0')
                ->where('status', '1') // 1表示开放报名
                ->find();
            
            if (!$course) {
                $this->error('课程不存在或暂不开放报名');
            }
            
            // 检查是否已满员
            if ($course['signed_people'] + count($peopleIds) > $course['max_people']) {
                $this->error('报名人数超出课程限制');
            }
            
            // 检查是否已报名
            $existCount = CourseSubscribe::where('course_session_id', $courseId)
                ->where('user_id', $userId)
                ->where('del_flag', '0')
                ->count();
            
            if ($existCount > 0) {
                $this->error('您已报名该课程');
            }
            
            // 开始事务
            Db::startTrans();
            
            try {
                // 批量插入报名记录
                $insertData = [];
                foreach ($peopleIds as $peopleId) {
                    $insertData[] = [
                        'course_session_id' => $courseId,
                        'user_id' => $userId,
                        'user_linkman_id' => $peopleId,
                        'emergency_contact' => $data['emergencyContact'] ?? '',
                        'emergency_phone' => $data['emergencyPhone'] ?? '',
                        'subscribe_time' => date('Y-m-d H:i:s'),
                        'subscribe_state' => '1', // 1表示已报名
                        'del_flag' => '0',
                        'create_time' => date('Y-m-d H:i:s')
                    ];
                }
                
                (new CourseSubscribe())->insertAll($insertData);
                
                // 更新课程已报名人数
                CourseSessionModel::where('id', $courseId)
                    ->setInc('signed_people', count($peopleIds));
                
                Db::commit();
                $this->success('报名成功');
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            $this->error('报名失败: ' . $e->getMessage());
        }
    }

    /**
     * 取消课程报名
     * 
     * @ApiTitle    (取消课程报名)
     * @ApiSummary  (取消指定人员的课程报名)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/cancelSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="courseId", type="integer", required=true, description="课程ID")
     * @ApiParams   (name="peopleIds", type="string", required=true, description="人员ID，多个用逗号分隔")
     */
    public function cancelSign()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }
            
            $courseId = $this->request->param('courseId');
            $peopleIds = $this->request->param('peopleIds');
            
            if (!$courseId) {
                $this->error('课程ID不能为空');
            }
            
            if (!$peopleIds) {
                $this->error('人员ID不能为空');
            }
            
            $peopleIdArray = is_array($peopleIds) ? $peopleIds : explode(',', $peopleIds);
            
            // 开始事务
            Db::startTrans();
            
            try {
                // 取消报名（软删除）
                $cancelCount = CourseSubscribe::where('course_session_id', $courseId)
                    ->where('user_id', $userId)
                    ->where('user_linkman_id', 'in', $peopleIdArray)
                    ->where('del_flag', '0')
                    ->update([
                        'del_flag' => '2',
                        'update_time' => date('Y-m-d H:i:s')
                    ]);
                
                if ($cancelCount > 0) {
                    // 更新课程已报名人数
                    CourseSessionModel::where('id', $courseId)
                        ->setDec('signed_people', $cancelCount);
                }
                
                Db::commit();
                $this->success('取消成功', $cancelCount);
            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            $this->error('取消失败: ' . $e->getMessage());
        }
    }

    /**
     * 显示我的报名记录
     * 
     * @ApiTitle    (我的报名记录)
     * @ApiSummary  (获取当前用户的课程报名历史记录)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/showMySign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="pageNum", type="integer", required=false, description="页码")
     * @ApiParams   (name="pageSize", type="integer", required=false, description="每页数量")
     */
    public function showMySign()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }
            
            $pageNum = $this->request->param('pageNum', 1);
            $pageSize = $this->request->param('pageSize', 10);
            
            // 查询总数
            $total = CourseSubscribe::alias('cs')
                ->join('course_session css', 'cs.course_session_id = css.id', 'LEFT')
                ->join('course c', 'css.course_id = c.id', 'LEFT')
                ->where('cs.user_id', $userId)
                ->where('cs.del_flag', '0')
                ->count();
            
            $list = [];
            if ($total > 0) {
                $start = ($pageNum - 1) * $pageSize;
                
                // 查询列表
                $list = CourseSubscribe::alias('cs')
                    ->join('course_session css', 'cs.course_session_id = css.id', 'LEFT')
                    ->join('course c', 'css.course_id = c.id', 'LEFT')
                    ->join('user_linkman ul', 'cs.user_linkman_id = ul.id', 'LEFT')
                    ->field([
                        'cs.id',
                        'cs.course_session_id',
                        'cs.subscribe_time',
                        'cs.subscribe_state',
                        'css.session_date',
                        'css.session_time_start',
                        'css.session_time_end',
                        'css.venue',
                        'c.course_name',
                        'c.course_type',
                        'ul.linkman_name',
                        'ul.linkman_phone'
                    ])
                    ->where('cs.user_id', $userId)
                    ->where('cs.del_flag', '0')
                    ->order('cs.subscribe_time desc')
                    ->limit($start, $pageSize)
                    ->select();
            }
            
            $result = [
                'total' => $total,
                'rows' => $list
            ];
            
            $this->success('获取成功', $result);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 个人中心课程签到
     * 
     * @ApiTitle    (课程签到)
     * @ApiSummary  (在个人中心进行课程签到操作)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/centerSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="courseId", type="integer", required=true, description="课程ID")
     */
    public function centerSign()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }
            
            $courseId = $this->request->param('courseId');
            
            if (!$courseId) {
                $this->error('课程ID不能为空');
            }
            
            // 查询用户是否已报名该课程
            $subscribe = CourseSubscribe::where('course_session_id', $courseId)
                ->where('user_id', $userId)
                ->where('del_flag', '0')
                ->find();
            
            if (!$subscribe) {
                $this->error('您未报名该课程');
            }
            
            if ($subscribe['subscribe_state'] == '2') {
                $this->error('您已签到过该课程');
            }
            
            // 更新签到状态
            $result = CourseSubscribe::where('id', $subscribe['id'])
                ->update([
                    'subscribe_state' => '2', // 2表示已签到
                    'sign_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            
            if ($result) {
                $this->success('签到成功');
            } else {
                $this->error('签到失败');
            }
        } catch (\Exception $e) {
            $this->error('签到失败: ' . $e->getMessage());
        }
    }

    /**
     * 显示课程凭证
     * 
     * @ApiTitle    (课程凭证)
     * @ApiSummary  (获取课程参与凭证信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/showVoucher)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="courseId", type="integer", required=true, description="课程ID")
     */
    public function showVoucher()
    {
        try {
            $userId = $this->getUserId();
            if (!$userId) {
                $this->error('用户未登录', null, 401);
            }
            
            $courseId = $this->request->param('courseId');
            
            if (!$courseId) {
                $this->error('课程ID不能为空');
            }
            
            // 查询课程凭证信息
            $voucher = CourseSubscribe::alias('cs')
                ->join('course_session css', 'cs.course_session_id = css.id', 'LEFT')
                ->join('course c', 'css.course_id = c.id', 'LEFT')
                ->join('user_linkman ul', 'cs.user_linkman_id = ul.id', 'LEFT')
                ->field([
                    'cs.id',
                    'cs.subscribe_time',
                    'cs.subscribe_state',
                    'cs.sign_time',
                    'css.session_date',
                    'css.session_time_start',
                    'css.session_time_end',
                    'css.venue',
                    'c.course_name',
                    'c.course_desc',
                    'c.course_type',
                    'ul.linkman_name',
                    'ul.linkman_phone'
                ])
                ->where('cs.course_session_id', $courseId)
                ->where('cs.user_id', $userId)
                ->where('cs.del_flag', '0')
                ->find();
            
            if (!$voucher) {
                $this->error('未找到课程凭证信息');
            }
            
            // 生成二维码内容（可以是课程ID+用户ID的组合）
            $voucher['qr_code'] = base64_encode($courseId . '_' . $userId . '_' . time());
            
            $this->success('获取成功', $voucher);
        } catch (\Exception $e) {
            $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户ID
     */

    /**
     * 获取当前用户ID
     */
    private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }

    /**
     * RSA公钥加密手机号
     * 对应Java中的RsaUtils.encryptByPublicKey方法
     * 
     * @param string $phone 原始手机号
     * @return string 加密后的手机号
     * @throws \Exception
     */
    private function rsaEncrypt($phone)
    {
        try {
            return RsaUtils::encryptByPublicKey(RsaUtils::$publicKey, $phone);
        } catch (\Exception $e) {
            throw new \Exception('RSA加密失败: ' . $e->getMessage());
        }
    }

    /**
     * RSA私钥解密手机号
     * 对应Java中的RsaUtils.decryptByPrivateKey方法
     * 
     * @param string $encryptedPhone 加密后的手机号
     * @return string 解密后的手机号
     * @throws \Exception
     */
    private function rsaDecrypt($encryptedPhone)
    {
        try {
            return RsaUtils::decryptByPrivateKey($encryptedPhone);
        } catch (\Exception $e) {
            throw new \Exception('RSA解密失败: ' . $e->getMessage());
        }
    }

}