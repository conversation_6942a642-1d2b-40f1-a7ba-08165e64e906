<?php

namespace app\apitp\controller;

use app\common\controller\Api;


/**
 * 课程场次管理接口
 */
class CourseSession extends Api
{
    // 所有接口都需要登录
    protected $noNeedLogin = [];
    // 所有接口都需要验证权限
    protected $noNeedRight = '*';

  
  


    /**
     * 显示我的报名记录
     * @ApiTitle    (显示我的报名记录)
     * @ApiSummary  (获取用户的课程报名和签到记录，支持分页)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/showMySign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="pageNum", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="pageSize", type="integer", required=false, description="每页数量，默认10")
     */
    public function showMySign()
    {
        // 获取用户ID
        $userId = $this->getUserId();

        // 获取分页参数 
        $pageNum = $this->request->param('pageNum', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 10, 'intval');

        // 查询总数 
        $total = $this->getMySignedCount($userId);
        $list = [];

        if ($total > 0) {
            // 计算分页参数 
            $start = ($pageNum - 1) * $pageSize;
            $end = $pageSize;

            // 查询分页数据
            $list = $this->getMySignList($userId, $start, $end);

            // 处理过期状态 
            $currentTime = time();
            foreach ($list as &$item) {
                // 如果flag为"1"且当前时间已超过课程结束时间，将flag更新为"2"
                if ($item['flag'] == '1' && $currentTime > strtotime($item['courseEndTime'])) {
                    $item['flag'] = '2';
                }
            }
        }

        // 返回结果 
        $result = [
            'total' => $total,
            'rows' => $list
        ];

        return $this->success('获取成功', $result);
    }




    /**
     * 获取用户课程报名记录总数
     * @param int $userId 用户ID
     * @return int
     */
    private function getMySignedCount($userId)
    {
        // 对应Java中的selectMySignedCount SQL查询
        $sql = "SELECT COUNT(*) as count FROM (
                    SELECT course_session_id, user_id
                    FROM course_subscribe
                    WHERE user_id = ?
                    GROUP BY course_session_id, user_id, sign_state, del_flag
                ) tem";

        $result = \think\Db::query($sql, [$userId]);
        return $result[0]['count'] ?? 0;
    }

    /**
     * 获取用户课程报名记录列表
     * @param int $userId 用户ID
     * @param int $start 偏移量
     * @param int $end 每页数量
     * @return array
     */
    private function getMySignList($userId, $start, $end)
    {
       
        $sql = "SELECT c.id as courseId, c.course_age_prop as courseAgeProp, c.course_cover as courseCover,
                       c.course_start_time as courseStartTime, c.course_end_time as courseEndTime,
                       c.week as weeks, c.course_name as courseName, c.course_address as courseAddress,
                       s.sign_state as signState, c.week, IF(s.del_flag = '0', '1', '0') as flag,
                       s.course_session_id, s.user_id, COUNT(l.linkman_name) as peopleCount
                FROM course_subscribe s
                LEFT JOIN course_session c ON c.id = s.course_session_id
                LEFT JOIN user_linkman l ON l.id = s.user_linkman_id
                WHERE s.user_id = ?
                GROUP BY c.course_start_time, c.course_end_time, c.course_name, c.course_address,
                         s.sign_state, c.week, IF(s.del_flag = '0', '1', '0'), s.course_session_id, s.user_id
                ORDER BY IF(s.del_flag = '0', '1', '0') DESC, c.course_start_time DESC
                LIMIT ?, ?";

        $result = \think\Db::query($sql, [$userId, $start, $end]);

        // 格式化数据
        foreach ($result as &$item) {
            // 时间格式化 
            if ($item['courseStartTime']) {
                $item['courseStartTime'] = date('Y/m/d H:i:s', strtotime($item['courseStartTime']));
            }
            if ($item['courseEndTime']) {
                $item['courseEndTime'] = date('Y/m/d H:i:s', strtotime($item['courseEndTime']));
            }

            // 确保peopleCount是字符串类型
            $item['peopleCount'] = (string)$item['peopleCount'];
        }

        return $result;
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }

   

    

}